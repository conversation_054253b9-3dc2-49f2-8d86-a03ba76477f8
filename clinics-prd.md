# PRD: Vet Clinic Directory & Listings

**Version:** 1.0
**Date:** July 26, 2025
**Status:** Final

---

## 1. Feature Goal 🎯

To create a verifiable directory of veterinary clinics that adopters and rescuers can search, and to allow these clinics to list their services and post cats for adoption on the platform.

---

## 2. User Stories 🧑‍🤝‍🧑

- **As a Clinic Owner**, I want to create a detailed profile with my services, location, and hours, and get verified so that I can attract new clients and build trust.
- **As a Clinic Owner**, I want to list cats my clinic has rescued so that I can find them loving homes using the platform's reach.
- **As an Adopter or Rescuer**, I want to search for clinics by location and specific services so that I can easily find the right medical care for my cat.
- **As a Platform Admin**, I want to manually review and approve clinic applications so that I can ensure all listed clinics are legitimate and trustworthy.

---

## 3. Core Requirements & Functionality 📋

### Clinic Onboarding

- Users can apply for the `clinic` role.
- Ad<PERSON> must manually approve applications before a clinic profile is made public.

### Clinic Profile Management

- Clinics can create and edit their profile, which includes:
    - `name`, `address`, `phone`, `website` (from existing schema).
    - **`operating_hours`**: Stored as a structured **`JSONB`** object (e.g., `{"monday": "09:00-17:00", "tuesday": "09:00-17:00"}`).
    - **`services`**: Stored as a **`text[]`** (text array) to be used as filterable tags.
    - **`featured`**: A new `boolean` flag that can be set by an admin to mark the clinic as featured.

### Cat Management

- A verified user with the `clinic` role can create, update, and manage their own cat listings for adoption, just like a `rescuer` user.

### Clinic Search & Discovery

- A public-facing clinic directory page will be created.
- Users can filter clinics based on:
    1.  **Location** (`wilaya`, `commune`)
    2.  **Services** (matching against the `services` tags)
    3.  **Clinic Name**
- Clinics marked as `featured: true` will always appear at the top of results.
- All other results will be sorted by default by creation date in descending order (**newest first**).

---

## 4. Required Schema Changes 💾

The `clinicProfiles` table needs to be updated as follows:

```typescript
// drizzle-orm/pg-core
import { relations } from "drizzle-orm";
import {
	pgTable,
	serial,
	text,
	timestamp,
	boolean,
	integer,
	jsonb, // Use jsonb for better performance and indexing
} from "drizzle-orm/pg-core";
import { users } from "./users"; // Assuming users schema is in another file

export const clinicProfiles = pgTable("clinic_profiles", {
	id: serial("id").primaryKey(),
	userId: integer("user_id")
		.notNull()
		.references(() => users.id)
		.unique(),
	name: text("name").notNull(),
	address: text("address").notNull(),
	city: text("city").notNull(),
	state: text("state").notNull(),
	zip: text("zip").notNull(),
	phone: text("phone").notNull(),
	website: text("website"),

	// --- NEW & UPDATED FIELDS ---
	services: text("services").array(), // For filterable tags
	operatingHours: jsonb("operating_hours"), // For structured hours (e.g., {"monday": "09:00-17:00"})
	featured: boolean("featured").default(false).notNull(),

	createdAt: timestamp("created_at").defaultNow().notNull(),
	updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

export const clinicProfilesRelations = relations(clinicProfiles, ({ one }) => ({
	user: one(users, {
		fields: [clinicProfiles.userId],
		references: [users.id],
	}),
}));
```
