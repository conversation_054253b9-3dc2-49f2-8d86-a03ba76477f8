import { db } from "@/lib/db";
import { cats, users } from "@/lib/db/schema";
import { eq } from "drizzle-orm";

/**
 * Migration script to fix cats and users with purely numeric slugs
 * This addresses the ID/slug confusion issue by prefixing numeric cat slugs
 */
async function fixNumericSlugs() {
	console.log("🔍 Starting numeric slug fix migration...");

	try {
		// Find cats with purely numeric slugs
		console.log("📋 Finding cats with numeric slugs...");
		const allCats = await db.query.cats.findMany({
			columns: { id: true, name: true, slug: true },
		});

		const catsWithNumericSlugs = allCats.filter((cat) =>
			/^\d+$/.test(cat.slug)
		);

		console.log(
			`Found ${catsWithNumericSlugs.length} cats with purely numeric slugs`
		);

		// Fix each cat with numeric slug
		for (const cat of catsWithNumericSlugs) {
			const oldSlug = cat.slug;
			const newSlug = `cat-${oldSlug}`;

			console.log(
				`🐱 Fixing cat "${cat.name}" (ID: ${cat.id}): "${oldSlug}" → "${newSlug}"`
			);

			// Check if the new slug already exists
			const existingCat = await db.query.cats.findFirst({
				where: eq(cats.slug, newSlug),
			});

			if (existingCat && existingCat.id !== cat.id) {
				// If the prefixed slug already exists, add a counter
				let counter = 1;
				let uniqueSlug = `${newSlug}-${counter}`;

				while (true) {
					const conflictingCat = await db.query.cats.findFirst({
						where: eq(cats.slug, uniqueSlug),
					});

					if (!conflictingCat || conflictingCat.id === cat.id) {
						break;
					}

					counter++;
					uniqueSlug = `${newSlug}-${counter}`;

					// Safety check
					if (counter > 1000) {
						uniqueSlug = `${newSlug}-${Date.now()}`;
						break;
					}
				}

				console.log(
					`  ⚠️  Slug conflict detected, using: "${uniqueSlug}"`
				);
				await db
					.update(cats)
					.set({ slug: uniqueSlug })
					.where(eq(cats.id, cat.id));
			} else {
				// No conflict, use the simple prefixed slug
				await db
					.update(cats)
					.set({ slug: newSlug })
					.where(eq(cats.id, cat.id));
			}

			console.log(`  ✅ Updated successfully`);
		}

		// Find users with purely numeric slugs
		console.log("📋 Finding users with numeric slugs...");
		const allUsers = await db.query.users.findMany({
			columns: { id: true, name: true, slug: true },
		});

		const usersWithNumericSlugs = allUsers.filter((user) =>
			/^\d+$/.test(user.slug)
		);

		console.log(
			`Found ${usersWithNumericSlugs.length} users with purely numeric slugs`
		);

		// Fix each user with numeric slug
		for (const user of usersWithNumericSlugs) {
			const oldSlug = user.slug;
			// For users, we regenerate the slug from their name instead of prefixing
			const baseSlug = user.name
				.toLowerCase()
				.trim()
				.replace(/[^\u0600-\u06FFa-zA-Z0-9\s_-]/g, "")
				.replace(/[\s_]+/g, "-")
				.replace(/-+/g, "-")
				.replace(/^-+|-+$/g, "");

			// If the name-based slug is still numeric, add a prefix
			const newSlug = /^\d+$/.test(baseSlug) ? `user-${baseSlug}` : baseSlug;

			console.log(
				`👤 Fixing user "${user.name}" (ID: ${user.id}): "${oldSlug}" → "${newSlug}"`
			);

			// Check if the new slug already exists
			const existingUser = await db.query.users.findFirst({
				where: eq(users.slug, newSlug),
			});

			if (existingUser && existingUser.id !== user.id) {
				// If the new slug already exists, add a counter
				let counter = 1;
				let uniqueSlug = `${newSlug}-${counter}`;

				while (true) {
					const conflictingUser = await db.query.users.findFirst({
						where: eq(users.slug, uniqueSlug),
					});

					if (!conflictingUser || conflictingUser.id === user.id) {
						break;
					}

					counter++;
					uniqueSlug = `${newSlug}-${counter}`;

					// Safety check
					if (counter > 1000) {
						uniqueSlug = `${newSlug}-${Date.now()}`;
						break;
					}
				}

				console.log(
					`  ⚠️  Slug conflict detected, using: "${uniqueSlug}"`
				);
				await db
					.update(users)
					.set({ slug: uniqueSlug })
					.where(eq(users.id, user.id));
			} else {
				// No conflict, use the new slug
				await db
					.update(users)
					.set({ slug: newSlug })
					.where(eq(users.id, user.id));
			}

			console.log(`  ✅ Updated successfully`);
		}

		console.log("🎉 Numeric slug fix migration completed successfully!");
		console.log(
			`📊 Summary: Fixed ${catsWithNumericSlugs.length} cats and ${usersWithNumericSlugs.length} users`
		);
	} catch (error) {
		console.error("❌ Error during numeric slug fix migration:", error);
		throw error;
	}
}

// Export for use in other scripts
export { fixNumericSlugs };

// Allow running directly
if (require.main === module) {
	fixNumericSlugs()
		.then(() => {
			console.log("Migration completed successfully");
			process.exit(0);
		})
		.catch((error) => {
			console.error("Migration failed:", error);
			process.exit(1);
		});
}
