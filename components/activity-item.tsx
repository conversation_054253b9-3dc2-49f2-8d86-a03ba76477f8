import React from "react";
import Image from "next/image";
import { But<PERSON> } from "@/components/ui/button";
import { MessageCircle } from "lucide-react";
import { StatusBadge } from "@/components/status-badge";
import { useTranslations } from "next-intl";
import { Link } from "@/lib/i18n/navigation";

interface ActivityItemProps {
	type: "favorite" | "message";
	date: string | Date;
	cat?: {
		id: string;
		slug?: string;
		name: string;
		imageUrl?: string;
	};
	user?: {
		id: string;
		slug?: string;
		name: string;
		imageUrl?: string;
	};
	from?: {
		id: string;
		slug?: string;
		name: string;
		imageUrl?: string;
	};
	status?: string;
	showActions?: boolean;
	onMessage?: () => void;
}

export function ActivityItem({
	type,
	date,
	cat,
	user,
	from,
	status,
	showActions = false,
	onMessage,
}: ActivityItemProps) {
	const t = useTranslations("activity");
	const buttonT = useTranslations("buttons");

	// Determine the image to display
	const imageUrl =
		user?.imageUrl || cat?.imageUrl || from?.imageUrl || "/placeholder.svg";
	const imageAlt = user?.name || cat?.name || from?.name || "Activity";

	return (
		<div className="flex items-start space-x-4">
			<div className="relative h-10 w-10 rounded-md overflow-hidden shrink-0 bg-muted">
				<Image
					src={imageUrl}
					alt={imageAlt}
					fill
					className="object-cover"
					onError={(e) => {
						e.currentTarget.src = "/placeholder.svg";
					}}
				/>
			</div>
			<div className="flex-1">
				<div className="flex items-center justify-between">
					<p className="font-medium">
						{type === "favorite" && cat && (
							<>
								{t("youFavorited")}{" "}
								<Link
									href={`/cats/${cat.slug || cat.id}`}
									className="text-primary hover:underline"
								>
									{cat.name}
								</Link>
							</>
						)}
						{type === "message" && cat && (
							<>
								{t("conversationAbout")}{" "}
								<Link
									href={`/cats/${cat.slug || cat.id}`}
									className="text-primary hover:underline"
								>
									{cat.name}
								</Link>
							</>
						)}
						{type === "message" && from && (
							<>
								<Link
									href={`/users/${from.slug || from.id}`}
									className="text-primary hover:underline"
								>
									{from.name}
								</Link>{" "}
								{t("sentYouMessage")}
							</>
						)}
					</p>
					{status && <StatusBadge status={status} />}
				</div>
				<p className="text-sm text-muted-foreground">
					{new Date(date).toLocaleDateString()}
				</p>
				{showActions && onMessage && (
					<div className="flex mt-2">
						<Button
							size="sm"
							variant="outline"
							className="mr-2"
							onClick={onMessage}
						>
							<MessageCircle className="h-3.5 w-3.5 mr-1" />
							{buttonT("message")}
						</Button>
					</div>
				)}
			</div>
		</div>
	);
}
