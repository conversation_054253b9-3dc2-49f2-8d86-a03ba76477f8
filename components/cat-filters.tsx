"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
} from "@/components/ui/form";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { Slider } from "@/components/ui/slider";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { useEffect, useState } from "react";
import { api } from "@/lib/trpc/react";
import { Loader2, SlidersHorizontal, ArrowUpDown } from "lucide-react";
import {
	Dialog,
	DialogContent,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
	DialogFooter,
} from "@/components/ui/dialog";
import { useTranslations } from "next-intl";

const filterSchema = z.object({
	gender: z.enum(["all", "male", "female"]).optional(),
	ageMin: z.number().min(0).max(20).optional(),
	ageMax: z.number().min(0).max(20).optional(),
	breedId: z.string().optional(),
	wilayaId: z.string().optional(),
	communeId: z.string().optional(),
	available: z.boolean().optional(),
	specialNeeds: z.boolean().optional(),
	vaccinated: z.boolean().optional(),
	neutered: z.boolean().optional(),
	search: z.string().optional(),
	sort: z.enum(["newest", "oldest", "name_asc", "name_desc"]).optional(),
	healthLabel: z.any().optional(), // Added for form structure
	ageLabel: z.any().optional(), // Added for form structure
	locationLabel: z.any().optional(), // Added for form structure
});

type FilterValues = z.infer<typeof filterSchema>;

interface CatFiltersProps {
	filters: any; // Will be typed properly from the wrapper
	onFiltersChange: (newFilters: any) => void;
	onReset: () => void;
}

export function CatFilters({
	filters,
	onFiltersChange,
	onReset,
}: CatFiltersProps) {
	const [dialogOpen, setDialogOpen] = useState(false);
	const t = useTranslations("cats");
	const commonT = useTranslations("common");

	// Fetch breeds, wilayas, and communes using tRPC
	const { data: breeds, isLoading: isLoadingBreeds } =
		api.breeds.getAll.useQuery();
	const { data: wilayas, isLoading: isLoadingWilayas } =
		api.location.getWilayas.useQuery();
	const [selectedWilayaId, setSelectedWilayaId] = useState<
		string | undefined
	>(filters.wilayaId || undefined);
	const { data: communes, isLoading: isLoadingCommunes } =
		api.location.getCommunesByWilaya.useQuery(
			{
				wilayaId: selectedWilayaId
					? parseInt(selectedWilayaId)
					: undefined,
			},
			{ enabled: !!selectedWilayaId }
		);

	const form = useForm<FilterValues>({
		resolver: zodResolver(filterSchema),
		values: {
			gender: filters.gender,
			ageMin: filters.ageMin,
			ageMax: filters.ageMax,
			breedId: filters.breedId || "",
			wilayaId: filters.wilayaId || "",
			communeId: filters.communeId || "",
			available: filters.available,
			specialNeeds: filters.specialNeeds,
			vaccinated: filters.vaccinated,
			neutered: filters.neutered,
			search: filters.search,
			sort: filters.sort,
			healthLabel: undefined, // Added for form structure
			ageLabel: undefined, // Added for form structure
			locationLabel: undefined, // Added for form structure
		},
	});

	// Watch wilayaId for changes to load communes and sync with URL
	useEffect(() => {
		// Sync selectedWilayaId with URL parameter
		setSelectedWilayaId(filters.wilayaId || undefined);

		const subscription = form.watch((value, { name }) => {
			if (name === "wilayaId") {
				setSelectedWilayaId(value.wilayaId);
				// Reset commune selection when wilaya changes
				form.setValue("communeId", "");
			}
		});
		return () => subscription.unsubscribe();
	}, [form, filters.wilayaId]);

	function onSubmit(values: FilterValues) {
		// Update all filter parameters using the centralized handler
		onFiltersChange({
			gender: values.gender === "all" ? null : values.gender,
			ageMin: values.ageMin === 0 ? null : values.ageMin,
			ageMax: values.ageMax === 20 ? null : values.ageMax,
			breedId:
				values.breedId === "" || values.breedId === "all"
					? null
					: values.breedId,
			wilayaId:
				values.wilayaId === "" || values.wilayaId === "all"
					? null
					: values.wilayaId,
			communeId:
				values.communeId === "" || values.communeId === "all"
					? null
					: values.communeId,
			available: values.available || null,
			specialNeeds: values.specialNeeds || null,
			vaccinated: values.vaccinated || null,
			neutered: values.neutered || null,
			sort: values.sort === "newest" ? null : values.sort,
		});

		setDialogOpen(false);
	}

	function resetFilters() {
		// Call the centralized reset function
		onReset();
		setDialogOpen(false);
	}

	// Handle sort change separately for immediate feedback
	const handleSortChange = (value: string) => {
		const sortValue = value as
			| "newest"
			| "oldest"
			| "name_asc"
			| "name_desc";
		onFiltersChange({
			sort: sortValue === "newest" ? null : sortValue,
		});
	};

	// Toggle filter functions for quick access filters
	const toggleGender = (gender: "male" | "female") => {
		const currentGender = filters.gender;
		const newGender = currentGender === gender ? "all" : gender;
		onFiltersChange({
			gender: newGender === "all" ? null : newGender,
		});
	};

	const toggleAvailable = () => {
		form.setValue("available", !form.getValues("available"));
		form.handleSubmit(onSubmit)();
	};

	const toggleVaccinated = () => {
		form.setValue("vaccinated", !form.getValues("vaccinated"));
		form.handleSubmit(onSubmit)();
	};

	const toggleNeutered = () => {
		form.setValue("neutered", !form.getValues("neutered"));
		form.handleSubmit(onSubmit)();
	};

	// Safely access form values with nullish coalescing
	const gender = form.watch("gender") ?? "all";
	const vaccinated = form.watch("vaccinated") ?? false;
	const neutered = form.watch("neutered") ?? false;
	const specialNeeds = form.watch("specialNeeds") ?? false;
	const available = form.watch("available") ?? false;
	const breedId = form.watch("breedId") ?? "";
	const wilayaId = form.watch("wilayaId") ?? "";
	const communeId = form.watch("communeId") ?? "";
	const ageMin = form.watch("ageMin") ?? 0;
	const sort = form.watch("sort") ?? "newest";

	const advancedFiltersCount =
		(specialNeeds ? 1 : 0) +
		(breedId !== "" && breedId !== "all" ? 1 : 0) +
		(wilayaId !== "" && wilayaId !== "all" ? 1 : 0) +
		(communeId !== "" && communeId !== "all" ? 1 : 0) +
		(ageMin > 0 ? 1 : 0);

	const getSortLabel = (sortValue: string) => {
		switch (sortValue) {
			case "newest":
				return t("sort.newest");
			case "oldest":
				return t("sort.oldest");
			case "name_asc":
				return t("sort.nameAZ");
			case "name_desc":
				return t("sort.nameZA");
			default:
				return t("sort.newest");
		}
	};

	// Determine if any filters are active to show the "Reset All" button
	const showResetAll =
		vaccinated ||
		neutered ||
		gender !== "all" ||
		available ||
		specialNeeds ||
		(breedId !== "" && breedId !== "all") ||
		(wilayaId !== "" && wilayaId !== "all") ||
		ageMin > 0 ||
		sort !== "newest" ||
		(filters.search &&
			typeof filters.search === "string" &&
			filters.search.trim());

	return (
		<div className="w-full">
			<div className="flex justify-between items-center gap-2">
				{/* Quick filter buttons row - left side */}
				<div className="flex flex-wrap gap-1">
					<Button
						variant={available ? "default" : "outline"}
						size="sm"
						className="rounded-full"
						onClick={toggleAvailable}
					>
						{t("filters.available")}
					</Button>

					<Button
						variant={vaccinated ? "default" : "outline"}
						size="sm"
						className="rounded-full"
						onClick={toggleVaccinated}
					>
						{t("filters.vaccinated")}
					</Button>

					<Button
						variant={neutered ? "default" : "outline"}
						size="sm"
						className="rounded-full"
						onClick={toggleNeutered}
					>
						{t("filters.neutered")}
					</Button>

					<Button
						variant={gender === "male" ? "default" : "outline"}
						size="sm"
						className="rounded-full"
						onClick={() => toggleGender("male")}
					>
						{t("filters.male")}
					</Button>

					<Button
						variant={gender === "female" ? "default" : "outline"}
						size="sm"
						className="rounded-full"
						onClick={() => toggleGender("female")}
					>
						{t("filters.female")}
					</Button>
				</div>

				{/* Sort and advanced filters - right side */}
				<div className="flex items-center gap-2">
					<Select value={sort} onValueChange={handleSortChange}>
						<SelectTrigger>
							<div className="flex items-center gap-1">
								<ArrowUpDown className="h-3.5 w-3.5" />
								<span>
									{t("filters.sortBy")}: {getSortLabel(sort)}
								</span>
							</div>
						</SelectTrigger>
						<SelectContent>
							<SelectItem value="newest">
								{t("sort.newest")}
							</SelectItem>
							<SelectItem value="oldest">
								{t("sort.oldest")}
							</SelectItem>
							<SelectItem value="name_asc">
								{t("sort.nameAZ")}
							</SelectItem>
							<SelectItem value="name_desc">
								{t("sort.nameZA")}
							</SelectItem>
						</SelectContent>
					</Select>

					<Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
						<DialogTrigger asChild>
							<Button
								variant="outline"
								size="sm"
								className="flex items-center gap-2"
							>
								<SlidersHorizontal className="h-4 w-4" />
								{t("filters.moreFilters")}
								{advancedFiltersCount > 0 && (
									<span className="inline-flex items-center justify-center w-5 h-5 rounded-full bg-primary text-primary-foreground text-xs">
										{advancedFiltersCount}
									</span>
								)}
							</Button>
						</DialogTrigger>
						<DialogContent className="sm:max-w-[425px]">
							<DialogHeader>
								<DialogTitle>{t("filters.title")}</DialogTitle>
							</DialogHeader>
							<Form {...form}>
								<form
									onSubmit={form.handleSubmit(onSubmit)}
									className="space-y-6"
								>
									<FormField
										control={form.control}
										name="breedId"
										render={({ field }) => (
											<FormItem>
												<FormLabel>
													{t("filters.breed")}
												</FormLabel>
												<Select
													onValueChange={
														field.onChange
													}
													defaultValue={
														field.value || "all"
													}
												>
													<FormControl>
														<SelectTrigger>
															<SelectValue
																placeholder={t(
																	"filters.selectBreed"
																)}
															/>
														</SelectTrigger>
													</FormControl>
													<SelectContent>
														<SelectItem value="all">
															{commonT("any")}
														</SelectItem>
														{isLoadingBreeds ? (
															<SelectItem
																value="loading"
																disabled
															>
																<Loader2 className="h-4 w-4 animate-spin mr-2" />
																{commonT(
																	"loading"
																)}{" "}
																{t(
																	"filters.breeds"
																)}
																...
															</SelectItem>
														) : (
															breeds?.map(
																(breed) => (
																	<SelectItem
																		key={
																			breed.id
																		}
																		value={breed.id.toString()}
																	>
																		{
																			breed.name
																		}
																	</SelectItem>
																)
															)
														)}
													</SelectContent>
												</Select>
											</FormItem>
										)}
									/>

									<FormField
										control={form.control}
										name="locationLabel"
										render={() => (
											<FormItem>
												<FormLabel>
													{t("filters.location")}
												</FormLabel>
												<div className="pt-2 space-y-4">
													<FormField
														control={form.control}
														name="wilayaId"
														render={({ field }) => (
															<FormItem>
																<FormLabel className="text-sm">
																	{t(
																		"filters.wilaya"
																	)}
																</FormLabel>
																<Select
																	onValueChange={
																		field.onChange
																	}
																	defaultValue={
																		field.value ||
																		"all"
																	}
																>
																	<FormControl>
																		<SelectTrigger>
																			<SelectValue
																				placeholder={t(
																					"filters.selectWilaya"
																				)}
																			/>
																		</SelectTrigger>
																	</FormControl>
																	<SelectContent>
																		<SelectItem value="all">
																			{commonT(
																				"any"
																			)}
																		</SelectItem>
																		{isLoadingWilayas ? (
																			<SelectItem
																				value="loading"
																				disabled
																			>
																				<Loader2 className="h-4 w-4 animate-spin mr-2" />
																				{commonT(
																					"loading"
																				)}{" "}
																				{t(
																					"filters.wilayas"
																				)}
																				...
																			</SelectItem>
																		) : (
																			wilayas?.map(
																				(
																					wilaya
																				) => (
																					<SelectItem
																						key={
																							wilaya.id
																						}
																						value={wilaya.id.toString()}
																					>
																						{
																							wilaya.code
																						}{" "}
																						-{" "}
																						{
																							wilaya.name
																						}
																					</SelectItem>
																				)
																			)
																		)}
																	</SelectContent>
																</Select>
															</FormItem>
														)}
													/>

													<FormField
														control={form.control}
														name="communeId"
														render={({ field }) => (
															<FormItem>
																<FormLabel className="text-sm">
																	{t(
																		"filters.commune"
																	)}
																</FormLabel>
																<Select
																	onValueChange={
																		field.onChange
																	}
																	defaultValue={
																		field.value ||
																		"all"
																	}
																	disabled={
																		!selectedWilayaId
																	}
																>
																	<FormControl>
																		<SelectTrigger>
																			<SelectValue
																				placeholder={
																					selectedWilayaId
																						? t(
																								"filters.selectCommune"
																							)
																						: t(
																								"filters.selectWilayaFirst"
																							)
																				}
																			/>
																		</SelectTrigger>
																	</FormControl>
																	<SelectContent>
																		<SelectItem value="all">
																			{commonT(
																				"any"
																			)}
																		</SelectItem>
																		{isLoadingCommunes ? (
																			<SelectItem
																				value="loading"
																				disabled
																			>
																				<Loader2 className="h-4 w-4 animate-spin mr-2" />
																				{commonT(
																					"loading"
																				)}{" "}
																				{t(
																					"filters.communes"
																				)}
																				...
																			</SelectItem>
																		) : (
																			communes?.map(
																				(
																					commune
																				) => (
																					<SelectItem
																						key={
																							commune.id
																						}
																						value={commune.id.toString()}
																					>
																						{
																							commune.name
																						}
																					</SelectItem>
																				)
																			)
																		)}
																	</SelectContent>
																</Select>
															</FormItem>
														)}
													/>
												</div>
											</FormItem>
										)}
									/>

									<FormField
										control={form.control}
										name="ageLabel"
										render={() => (
											<FormItem>
												<FormLabel>
													{t("filters.ageRange")} (
													{t("filters.years")})
												</FormLabel>
												<div className="pt-4 space-y-4">
													<FormField
														control={form.control}
														name="ageMin"
														render={({ field }) => (
															<FormItem>
																<FormControl>
																	<Slider
																		min={0}
																		max={20}
																		step={1}
																		value={[
																			field.value ??
																				0,
																		]}
																		onValueChange={(
																			value
																		) =>
																			field.onChange(
																				value[0]
																			)
																		}
																	/>
																</FormControl>
															</FormItem>
														)}
													/>
													<FormField
														control={form.control}
														name="ageMax"
														render={({ field }) => (
															<FormItem className="hidden">
																<FormControl>
																	<Input
																		type="hidden"
																		{...field}
																	/>
																</FormControl>
															</FormItem>
														)}
													/>
													<div className="flex justify-between text-sm">
														<span>
															{ageMin}{" "}
															{t("filters.years")}
														</span>
														<span>
															{t("filters.to")}
														</span>
														<span>
															{form.watch(
																"ageMax"
															) ?? 20}{" "}
															{t("filters.years")}
														</span>
													</div>
												</div>
											</FormItem>
										)}
									/>

									<FormField
										control={form.control}
										name="specialNeeds"
										render={({ field }) => (
											<FormItem className="flex flex-row items-center space-x-3 space-y-0">
												<FormControl>
													<Checkbox
														checked={field.value}
														onCheckedChange={
															field.onChange
														}
													/>
												</FormControl>
												<div className="font-normal">
													{t("filters.specialNeeds")}
												</div>
											</FormItem>
										)}
									/>

									<DialogFooter className="flex-col sm:flex-col gap-2 mt-6">
										<Button
											type="submit"
											className="w-full"
										>
											{t("filters.apply")}
										</Button>
										<Button
											type="button"
											variant="outline"
											onClick={resetFilters}
											className="w-full"
										>
											{t("filters.clear")}
										</Button>
									</DialogFooter>
								</form>
							</Form>
						</DialogContent>
					</Dialog>

					{showResetAll && (
						<Button
							variant="ghost"
							size="sm"
							className="text-xs"
							onClick={resetFilters}
						>
							{t("filters.resetAll")}
						</Button>
					)}
				</div>
			</div>
		</div>
	);
}
