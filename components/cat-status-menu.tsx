"use client";

import { useState } from "react";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { ChevronDown, Loader2 } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { api } from "@/lib/trpc/react";
import { useRouter } from "next/navigation";
import { useTranslations, useLocale } from "next-intl";
import { cn } from "@/lib/utils";

interface CatStatusMenuProps {
	catId: string;
	currentStatus: string;
	onStatusChange?: (newStatus: string) => void;
	catName?: string;
	className?: string;
}

export function CatStatusMenu({
	catId,
	currentStatus,
	onStatusChange,
	catName,
	className,
}: CatStatusMenuProps) {
	const [isOpen, setIsOpen] = useState(false);
	const [isPending, setIsPending] = useState(false);
	const { toast } = useToast();
	const router = useRouter();
	const utils = api.useUtils();
	const t = useTranslations("cats");
	const commonT = useTranslations("common");
	const locale = useLocale();

	// Determine text direction based on locale
	const isRTL = locale === "ar";

	const { mutate: updateStatus } = api.cats.updateStatus.useMutation({
		onMutate: () => {
			setIsPending(true);
		},
		onSuccess: (data) => {
			toast({
				title: t("status.updateSuccess"),
				description: t("status.updateDescription", {
					status: data.status,
				}),
			});

			// Invalidate related queries to refresh data
			utils.cats.getById.invalidate(catId);
			utils.cats.getUserCats.invalidate();
			utils.messages.getChatMessages.invalidate();
			router.refresh();

			// Call the parent's callback if provided
			if (onStatusChange) {
				onStatusChange(data.status);
			}

			setIsPending(false);
			setIsOpen(false);
		},
		onError: (error) => {
			toast({
				title: commonT("error"),
				description: error.message || t("status.updateError"),
				variant: "destructive",
			});
			setIsPending(false);
		},
	});

	const handleStatusChange = (status: string) => {
		updateStatus({
			id: catId,
			status: status as
				| "available"
				| "pending"
				| "adopted"
				| "unavailable",
		});
	};

	// Helper function to get status display text
	const getStatusText = (status: string) => {
		switch (status.toLowerCase()) {
			case "available":
				return t("status.available");
			case "pending":
				return t("status.pending");
			case "adopted":
				return t("status.adopted");
			case "unavailable":
				return t("status.unavailable");
			default:
				return status;
		}
	};

	// Helper function to get status colors
	const getStatusColors = (status: string) => {
		switch (status.toLowerCase()) {
			case "available":
				return "bg-green-100 text-green-700 border-green-200";
			case "pending":
				return "bg-yellow-100 text-yellow-700 border-yellow-200";
			case "adopted":
				return "bg-blue-100 text-blue-700 border-blue-200";
			case "unavailable":
				return "bg-red-100 text-red-700 border-red-200";
			default:
				return "bg-gray-100 text-gray-700 border-gray-200";
		}
	};

	return (
		<DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
			<DropdownMenuTrigger asChild disabled={isPending}>
				<Button
					variant="outline"
					className={cn(
						className,
						"flex cursor-pointer items-center gap-2 min-h-[44px] px-3 py-2 rounded-lg border transition-colors",
						"hover:bg-gray-50 focus:ring-2 focus:ring-ring focus:ring-offset-2",
						"disabled:opacity-50 disabled:cursor-not-allowed",
						getStatusColors(currentStatus),
						isRTL && "flex-row-reverse"
					)}
					aria-label={t("status.changeStatus")}
				>
					{isPending ? (
						<Loader2 className="h-4 w-4 animate-spin" />
					) : (
						<>
							<span className="text-sm font-medium">
								{getStatusText(currentStatus)}
							</span>
							<ChevronDown
								className={cn(
									"h-4 w-4 transition-transform duration-200",
									isOpen && "rotate-180"
								)}
							/>
						</>
					)}
				</Button>
			</DropdownMenuTrigger>
			<DropdownMenuContent
				align={isRTL ? "start" : "end"}
				className={cn(
					"w-48 p-3 rounded-lg border shadow-lg bg-white",
					isRTL && "rtl"
				)}
				sideOffset={8}
			>
				<div className="mb-2">
					<h3
						className={cn(
							"text-sm font-semibold text-gray-900 mb-1",
							isRTL ? "text-right" : "text-left"
						)}
					>
						{t("status.changeStatus")}
					</h3>
					<p
						className={cn(
							"text-xs text-gray-500",
							isRTL ? "text-right" : "text-left"
						)}
					>
						{catName
							? `Update ${catName}'s availability`
							: "Update availability"}
					</p>
				</div>

				<div className="space-y-2">
					<StatusMenuItem
						status="available"
						currentStatus={currentStatus}
						onClick={() => handleStatusChange("available")}
						isRTL={isRTL}
						getStatusText={getStatusText}
						getStatusColors={getStatusColors}
					/>

					<StatusMenuItem
						status="pending"
						currentStatus={currentStatus}
						onClick={() => handleStatusChange("pending")}
						isRTL={isRTL}
						getStatusText={getStatusText}
						getStatusColors={getStatusColors}
					/>

					<StatusMenuItem
						status="adopted"
						currentStatus={currentStatus}
						onClick={() => handleStatusChange("adopted")}
						isRTL={isRTL}
						getStatusText={getStatusText}
						getStatusColors={getStatusColors}
					/>

					<StatusMenuItem
						status="unavailable"
						currentStatus={currentStatus}
						onClick={() => handleStatusChange("unavailable")}
						isRTL={isRTL}
						getStatusText={getStatusText}
						getStatusColors={getStatusColors}
					/>
				</div>
			</DropdownMenuContent>
		</DropdownMenu>
	);
}

interface StatusMenuItemProps {
	status: string;
	currentStatus: string;
	onClick: () => void;
	isRTL: boolean;
	getStatusText: (status: string) => string;
	getStatusColors: (status: string) => string;
}

function StatusMenuItem({
	status,
	currentStatus,
	onClick,
	isRTL,
	getStatusText,
	getStatusColors,
}: StatusMenuItemProps) {
	const isActive = status.toLowerCase() === currentStatus.toLowerCase();

	return (
		<DropdownMenuItem
			onClick={onClick}
			disabled={isActive}
			className={cn(
				"flex items-center justify-center min-h-[44px] px-3 py-2 rounded-lg transition-all duration-200",
				"hover:bg-gray-50 focus:bg-gray-50 focus:outline-none cursor-pointer",
				"disabled:cursor-default",
				isRTL && "flex-row-reverse"
			)}
		>
			<div
				className={cn(
					"px-3 py-1.5 rounded-md text-sm font-medium border transition-colors w-full text-center",
					getStatusColors(status),
					isActive && "ring-2 ring-blue-500 ring-offset-1"
				)}
			>
				{getStatusText(status)}
			</div>
		</DropdownMenuItem>
	);
}
