"use client";

import * as React from "react";
import {
	Loader2,
	Upload,
	X,
	Plus,
	ImageIcon,
	AlertCircle,
	Info,
	Star,
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
	<PERSON>,
	CardContent,
	CardFooter,
	CardDescription,
} from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import Image from "next/image";
import { useCatForm } from "./cat-form-provider";
import { cn } from "@/lib/utils";
import { Separator } from "@/components/ui/separator";
import { useTranslations } from "next-intl";

interface CatFormImagesProps {
	isMobile: boolean;
	methods: any;
}

export function CatFormImages({ methods }: CatFormImagesProps) {
	const {
		images,
		getRootProps,
		getInputProps,
		removeImage,
		saveDraft,
		publishCat,
		isSubmitting,
		isSavingDraft,
		cat,
		imageErrors,
		setPrimaryImage,
	} = useCatForm();
	const t = useTranslations("forms.catForm");
	const catsT = useTranslations("cats");

	const maxImagesReached = images.length >= 5;

	return (
		<Card className="border-none shadow-none">
			<CardContent className="space-y-6 px-0">
				<div className="flex items-center">
					<h3 className="text-lg font-medium">{t("images.title")}</h3>
					<Separator className="flex-1 ml-3" />
				</div>

				<CardDescription className="text-sm">
					{t("images.description")}
				</CardDescription>

				<div className="grid grid-cols-1 gap-6">
					<div className="space-y-6">
						{imageErrors.length > 0 && (
							<Alert variant="destructive" className="mb-4">
								<AlertCircle className="h-4 w-4" />
								<AlertDescription>
									<ul className="list-disc pl-5 space-y-1">
										{imageErrors.map((error, index) => (
											<li key={index}>{error}</li>
										))}
									</ul>
								</AlertDescription>
							</Alert>
						)}

						{maxImagesReached && (
							<Alert className="mb-4 bg-muted">
								<Info className="h-4 w-4" />
								<AlertDescription>
									{t("images.maxReached")}
								</AlertDescription>
							</Alert>
						)}

						<div
							{...getRootProps()}
							className={cn(
								"border-2 border-dashed rounded-lg p-8 text-center transition-all duration-200",
								!maxImagesReached &&
									"cursor-pointer hover:bg-muted/50 hover:border-primary/50",
								maxImagesReached &&
									"opacity-50 bg-muted border-muted-foreground/30",
								"flex flex-col items-center justify-center",
								imageErrors.length > 0 && "border-destructive"
							)}
						>
							<input
								{...getInputProps()}
								disabled={maxImagesReached}
							/>
							<div className="flex flex-col items-center justify-center gap-3">
								<div className="p-3 rounded-full bg-muted">
									<Upload className="h-8 w-8 text-muted-foreground" />
								</div>
								<div>
									{maxImagesReached ? (
										<p className="font-medium text-muted-foreground">
											{t("images.maxReached")}
										</p>
									) : (
										<>
											<p className="font-medium text-muted-foreground">
												{t("images.dropzone")}
											</p>
											<p className="text-xs text-muted-foreground mt-1">
												{t("images.fileRequirements")}
											</p>
										</>
									)}
								</div>
							</div>
						</div>

						{images.length > 0 ? (
							<div>
								<div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-5 gap-4 mt-4">
									{images.map((image, index) => (
										<div
											key={index}
											className="relative group"
										>
											<div className="aspect-square relative overflow-hidden rounded-lg border bg-muted transition-all duration-200 group-hover:border-primary/50">
												<Image
													src={image.preview}
													alt={`${catsT("gallery.photo")} ${index + 1}`}
													fill
													className="object-cover"
												/>
											</div>
											<div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2 rounded-lg">
												<Button
													size="icon"
													variant="outline"
													className={cn(
														"h-8 w-8",
														image.isPrimary &&
															"bg-primary text-primary-foreground hover:bg-primary/90"
													)}
													type="button"
													onClick={() =>
														setPrimaryImage(index)
													}
													aria-label={t(
														"images.setPrimary"
													)}
												>
													<Star className="h-4 w-4" />
												</Button>
												<Button
													size="icon"
													variant="destructive"
													className="h-8 w-8"
													type="button"
													onClick={() =>
														removeImage(index)
													}
													aria-label={t(
														"images.remove"
													)}
												>
													<X className="h-4 w-4" />
												</Button>
											</div>
											{image.isPrimary && (
												<Badge className="absolute top-2 left-2 bg-primary">
													{t("images.primary")}
												</Badge>
											)}
										</div>
									))}

									{!maxImagesReached && (
										<div
											className={cn(
												"aspect-square flex items-center justify-center rounded-lg border border-dashed",
												"cursor-pointer hover:bg-muted/50 hover:border-primary/50 transition-all duration-200"
											)}
											{...getRootProps()}
										>
											<div className="flex flex-col items-center gap-1">
												<Plus className="h-6 w-6 text-muted-foreground" />
												<span className="text-xs text-muted-foreground">
													{t("images.add")}
												</span>
												<span className="text-xs text-muted-foreground">{`(${images.length}/5)`}</span>
											</div>
										</div>
									)}
								</div>

								<div className="mt-4 p-4 bg-muted/50 rounded-lg border border-muted">
									<div className="flex items-start gap-3">
										<div className="p-2 bg-background rounded-full">
											<ImageIcon className="h-5 w-5 text-muted-foreground" />
										</div>
										<div>
											<h4 className="text-sm font-medium">
												{t("images.tips.title")}
											</h4>
											<ul className="text-xs text-muted-foreground mt-1 list-disc pl-4 space-y-1">
												<li>{t("images.tips.tip1")}</li>
												<li>{t("images.tips.tip2")}</li>
												<li>{t("images.tips.tip3")}</li>
												<li>{t("images.tips.tip4")}</li>
											</ul>
										</div>
									</div>
								</div>
							</div>
						) : (
							<div className="p-4 bg-destructive/10 rounded-lg border border-destructive/30 text-center text-destructive">
								<AlertCircle className="h-5 w-5 mx-auto mb-2" />
								<p className="font-medium">
									{t("images.noImages")}
								</p>
							</div>
						)}
					</div>
				</div>
			</CardContent>
			<CardFooter className="flex justify-between px-0 pt-4">
				<Button
					type="button"
					variant="outline"
					onClick={() => methods.prev()}
				>
					{t("images.back")}
				</Button>
				<div className="flex gap-2">
					<Button
						type="button"
						variant="outline"
						onClick={saveDraft}
						disabled={isSubmitting}
					>
						{isSubmitting && isSavingDraft ? (
							<>
								<Loader2 className="mr-2 h-4 w-4 animate-spin" />
								{t("buttons.saving")}
							</>
						) : (
							t("buttons.saveDraft")
						)}
					</Button>
					<Button
						type="button"
						onClick={publishCat}
						disabled={isSubmitting || images.length === 0}
						className={cn(
							images.length === 0 &&
								"opacity-50 cursor-not-allowed"
						)}
					>
						{isSubmitting && !isSavingDraft ? (
							<>
								<Loader2 className="mr-2 h-4 w-4 animate-spin" />
								{cat
									? t("buttons.updating")
									: t("buttons.publishing")}
							</>
						) : cat ? (
							t("buttons.update")
						) : (
							t("buttons.publish")
						)}
					</Button>
				</div>
			</CardFooter>
		</Card>
	);
}
