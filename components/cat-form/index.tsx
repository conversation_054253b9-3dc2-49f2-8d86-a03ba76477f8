"use client";

import { useMediaQuery } from "@/hooks/use-media-query";
import { defineStepper } from "@/components/ui/stepper";
import { CatFormProvider } from "./cat-form-provider";
import { CatFormStepper } from "./cat-form-stepper";
import { CatFormInfo } from "./cat-form-info";
import { CatFormImages } from "./cat-form-images";
import { useTranslations } from "next-intl";

export function CatForm({ cat }: { cat?: any }) {
	const isMobile = useMediaQuery("(max-width: 768px)");
	const t = useTranslations("forms.catForm");

	// Define stepper with two steps
	const { Stepper, useStepper } = defineStepper(
		{ id: "info", title: t("steps.info") },
		{ id: "images", title: t("steps.images") }
	);

	return (
		<CatFormProvider cat={cat}>
			{(context) => (
				<div className="w-full max-w-3xl mx-auto p-4 sm:p-6 bg-background/50 backdrop-blur-xs rounded-xl shadow-xs border">
					<h1 className="text-2xl font-bold text-center mb-6">
						{cat ? t("editTitle") : t("newTitle")}
					</h1>

					<CatFormStepper
						Stepper={Stepper}
						useStepper={useStepper}
						isMobile={isMobile}
					>
						{({ methods }) => (
							<>
								{methods.switch({
									info: () => (
										<CatFormInfo
											isMobile={isMobile}
											methods={methods}
										/>
									),
									images: () => (
										<CatFormImages
											isMobile={isMobile}
											methods={methods}
										/>
									),
								})}
							</>
						)}
					</CatFormStepper>
				</div>
			)}
		</CatFormProvider>
	);
}
