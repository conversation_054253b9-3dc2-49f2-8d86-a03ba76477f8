"use client";

import * as React from "react";
import { Check } from "lucide-react";
import { cn } from "@/lib/utils";

interface CatFormStepperProps {
	Stepper: any;
	useStepper: any;
	isMobile: boolean;
	children: (props: { methods: any }) => React.ReactNode;
}

export function CatFormStepper({
	Stepper,
	useStepper,
	isMobile,
	children,
}: CatFormStepperProps) {
	return (
		<Stepper.Provider
			className="space-y-8"
			variant={isMobile ? "vertical" : "horizontal"}
		>
			{({ methods }: { methods: any }) => (
				<React.Fragment>
					<Stepper.Navigation className="mb-10">
						{methods.all.map((step: any) => {
							const isCompleted =
								methods.all.findIndex(
									(s: any) => s.id === step.id
								) <
								methods.all.findIndex(
									(s: any) => s.id === methods.current.id
								);
							const isCurrent = methods.current.id === step.id;

							return (
								<Stepper.Step
									key={step.id}
									of={step.id}
									onClick={() => methods.goTo(step.id)}
									className={cn(
										"transition-all duration-300",
										isCurrent && "scale-105"
									)}
								>
									<div
										className={cn(
											"w-12 h-12 rounded-full flex items-center justify-center border-2 transition-all duration-300",
											isCurrent
												? "border-primary bg-primary text-primary-foreground shadow-md"
												: isCompleted
													? "border-primary bg-primary/90 text-primary-foreground"
													: "border-muted-foreground/30 bg-background text-muted-foreground"
										)}
									>
										{isCompleted ? (
											<Check className="h-6 w-6" />
										) : (
											<span className="text-lg font-medium">
												{methods.all.findIndex(
													(s: any) => s.id === step.id
												) + 1}
											</span>
										)}
									</div>
									<Stepper.Title className="font-medium mt-2">
										{step.title}
									</Stepper.Title>
									{isMobile &&
										methods.when(step.id, (step: any) => (
											<Stepper.Panel>
												{children({ methods })}
											</Stepper.Panel>
										))}
								</Stepper.Step>
							);
						})}
					</Stepper.Navigation>

					{/* Content */}
					{!isMobile && (
						<div className="transition-all duration-300">
							{children({ methods })}
						</div>
					)}
				</React.Fragment>
			)}
		</Stepper.Provider>
	);
}
