"use client";

import * as React from "react";
import { OTPInput, OTPInputContext } from "input-otp";
import { Dot } from "lucide-react";

import { cn } from "@/lib/utils";

const InputOTP = ({
	className,
	containerClassName,
	...props
}: React.ComponentPropsWithoutRef<typeof OTPInput>) => (
	<OTPInput
		data-slot="input-otp"
		containerClassName={cn(
			"flex items-center gap-2 has-disabled:opacity-50",
			containerClassName
		)}
		className={cn("disabled:cursor-not-allowed", className)}
		{...props}
	/>
);
InputOTP.displayName = "InputOTP";

const InputOTPGroup = ({
	className,
	...props
}: React.ComponentPropsWithoutRef<"div">) => (
	<div
		data-slot="input-otp-group"
		className={cn("flex items-center", className)}
		{...props}
	/>
);
InputOTPGroup.displayName = "InputOTPGroup";

type InputOTPSlotProps = {
	index: number;
	className?: string;
} & React.ComponentPropsWithoutRef<"div">;

const InputOTPSlot = ({ index, className, ...props }: InputOTPSlotProps) => {
	const inputOTPContext = React.useContext(OTPInputContext);
	const { char, hasFakeCaret, isActive } = inputOTPContext.slots[index];

	return (
		<div
			data-slot="input-otp-slot"
			className={cn(
				"relative flex h-10 w-10 items-center justify-center border-y border-r border-input text-sm transition-all first:rounded-l-md first:border-l last:rounded-r-md",
				isActive && "z-10 ring-2 ring-ring ring-offset-background",
				className
			)}
			{...props}
		>
			{char}
			{hasFakeCaret && (
				<div className="pointer-events-none absolute inset-0 flex items-center justify-center">
					<div className="h-4 w-px animate-caret-blink bg-foreground duration-1000" />
				</div>
			)}
		</div>
	);
};
InputOTPSlot.displayName = "InputOTPSlot";

const InputOTPSeparator = ({
	...props
}: React.ComponentPropsWithoutRef<"div">) => (
	<div data-slot="input-otp-separator" role="separator" {...props}>
		<Dot />
	</div>
);
InputOTPSeparator.displayName = "InputOTPSeparator";

export { InputOTP, InputOTPGroup, InputOTPSlot, InputOTPSeparator };
