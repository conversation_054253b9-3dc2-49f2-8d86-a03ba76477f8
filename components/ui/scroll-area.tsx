"use client";

import * as React from "react";
import * as ScrollAreaPrimitive from "@radix-ui/react-scroll-area";

import { cn } from "@/lib/utils";

const ScrollArea = ({
	className,
	children,
	...props
}: React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.Root>) => (
	<ScrollAreaPrimitive.Root
		data-slot="scroll-area"
		className={cn("relative overflow-hidden", className)}
		{...props}
	>
		<ScrollAreaPrimitive.Viewport
			data-slot="scroll-area-viewport"
			className="h-full w-full rounded-[inherit]"
		>
			{children}
		</ScrollAreaPrimitive.Viewport>
		<ScrollBar />
		<ScrollAreaPrimitive.Corner />
	</ScrollAreaPrimitive.Root>
);
ScrollArea.displayName = ScrollAreaPrimitive.Root.displayName;

const ScrollBar = ({
	className,
	orientation = "vertical",
	...props
}: React.ComponentPropsWithoutRef<
	typeof ScrollAreaPrimitive.ScrollAreaScrollbar
>) => (
	<ScrollAreaPrimitive.ScrollAreaScrollbar
		data-slot="scroll-area-scrollbar"
		orientation={orientation}
		className={cn(
			"flex touch-none select-none transition-colors",
			orientation === "vertical" &&
				"h-full w-2.5 border-l border-l-transparent p-px",
			orientation === "horizontal" &&
				"h-2.5 flex-col border-t border-t-transparent p-px",
			className
		)}
		{...props}
	>
		<ScrollAreaPrimitive.ScrollAreaThumb
			data-slot="scroll-area-thumb"
			className="relative flex-1 rounded-full bg-border"
		/>
	</ScrollAreaPrimitive.ScrollAreaScrollbar>
);
ScrollBar.displayName = ScrollAreaPrimitive.ScrollAreaScrollbar.displayName;

export { ScrollArea, ScrollBar };
