"use client";

import { useState, useRef } from "react";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import { Camera, Trash2, Upload } from "lucide-react";
import { cn } from "@/lib/utils";

interface AvatarUploaderProps {
	currentImage: string | null;
	onImageChange: (imageData: string | null) => void;
	className?: string;
}

export function AvatarUploader({
	currentImage,
	onImageChange,
	className,
}: AvatarUploaderProps) {
	const [previewUrl, setPreviewUrl] = useState<string | null>(currentImage);
	const [isHovering, setIsHovering] = useState(false);
	const fileInputRef = useRef<HTMLInputElement>(null);

	const handleImageSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
		const file = e.target.files?.[0];
		if (!file) return;

		// Validate file is an image and not too large
		if (!file.type.startsWith("image/")) {
			alert("Please select an image file");
			return;
		}

		if (file.size > 5 * 1024 * 1024) {
			// 5MB limit
			alert("Image must be smaller than 5MB");
			return;
		}

		const reader = new FileReader();
		reader.onload = (event) => {
			const imageData = event.target?.result as string;
			setPreviewUrl(imageData);
			onImageChange(imageData);
		};
		reader.readAsDataURL(file);
	};

	const handleClick = () => {
		fileInputRef.current?.click();
	};

	const handleRemoveImage = () => {
		setPreviewUrl(null);
		onImageChange(null);
		if (fileInputRef.current) {
			fileInputRef.current.value = "";
		}
	};

	return (
		<div className={cn("flex flex-col items-center space-y-3", className)}>
			<div
				className="relative h-32 w-32 rounded-full overflow-hidden bg-muted cursor-pointer"
				onMouseEnter={() => setIsHovering(true)}
				onMouseLeave={() => setIsHovering(false)}
				onClick={handleClick}
			>
				{previewUrl ? (
					<>
						<Image
							src={previewUrl}
							alt="User avatar"
							fill
							className="object-cover"
							onError={(e) => {
								e.currentTarget.src =
									"https://placehold.co/400x400/e2e8f0/94a3b8?text=User";
							}}
						/>
						{isHovering && (
							<div className="absolute inset-0 bg-black/50 flex items-center justify-center">
								<Camera className="h-8 w-8 text-white" />
							</div>
						)}
					</>
				) : (
					<div className="absolute inset-0 flex items-center justify-center">
						<Upload className="h-8 w-8 text-muted-foreground" />
					</div>
				)}
			</div>

			<input
				type="file"
				ref={fileInputRef}
				className="hidden"
				accept="image/*"
				onChange={handleImageSelect}
			/>

			<div className="flex space-x-2">
				<Button
					type="button"
					variant="outline"
					size="sm"
					onClick={handleClick}
				>
					<Camera className="h-4 w-4 mr-2" />
					{previewUrl ? "Change" : "Upload"}
				</Button>

				{previewUrl && (
					<Button
						type="button"
						variant="outline"
						size="sm"
						onClick={handleRemoveImage}
						className="text-destructive border-destructive hover:bg-destructive/10"
					>
						<Trash2 className="h-4 w-4 mr-2" />
						Remove
					</Button>
				)}
			</div>
		</div>
	);
}
