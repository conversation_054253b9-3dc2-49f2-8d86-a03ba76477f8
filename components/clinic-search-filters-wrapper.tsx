"use client";

import { useState, useRef } from "react";
import { ClinicSearch } from "@/components/clinic-search";
import { ClinicFilters } from "@/components/clinic-filters";
import { useQueryStates } from "nuqs";
import { clinicListingParsers } from "@/lib/search-params";

export function ClinicSearchFiltersWrapper() {
	// Centralized state management for all filter parameters
	const [filters, setFilters] = useQueryStates(clinicListingParsers);

	// Local search input state for immediate UI updates
	const [searchInput, setSearchInput] = useState(filters.search);

	// Debounce timeout ref
	const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);

	// Handle search input changes with debouncing
	const handleSearchChange = (value: string) => {
		setSearchInput(value);

		// Clear existing timeout
		if (debounceTimeoutRef.current) {
			clearTimeout(debounceTimeoutRef.current);
		}

		// Set new timeout for debounced search
		debounceTimeoutRef.current = setTimeout(() => {
			setFilters({ 
				search: value,
				page: 1 // Reset to first page when searching
			});
		}, 300);
	};

	// Handle filter changes
	const handleFiltersChange = (newFilters: Partial<typeof filters>) => {
		setFilters({ 
			...newFilters,
			page: 1 // Reset to first page when filters change
		});
	};

	// Handle reset filters
	const handleReset = () => {
		setSearchInput("");
		setFilters({
			search: "",
			page: 1,
			wilayaId: "all",
			communeId: "all",
			featured: false,
			services: [],
			sort: "featured",
		});
	};

	return (
		<div className="space-y-6 mb-8">
			{/* Search Bar */}
			<div className="flex justify-center">
				<ClinicSearch
					search={searchInput}
					onSearchChange={handleSearchChange}
				/>
			</div>

			{/* Filters */}
			<ClinicFilters
				filters={filters}
				onFiltersChange={handleFiltersChange}
				onReset={handleReset}
			/>
		</div>
	);
}
