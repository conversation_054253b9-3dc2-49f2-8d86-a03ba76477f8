"use client";

import { Search, MessageCircle, Home } from "lucide-react";
import { useTranslations } from "next-intl";

export function HowItWorks() {
	const t = useTranslations("home");

	const steps = [
		{
			icon: Search,
			title: t("howItWorks.step1.title"),
			description: t("howItWorks.step1.description"),
		},
		{
			icon: MessageCircle,
			title: t("howItWorks.step2.title"),
			description: t("howItWorks.step2.description"),
		},
		{
			icon: Home,
			title: t("howItWorks.step3.title"),
			description: t("howItWorks.step3.description"),
		},
	];

	return (
		<section className="py-20 bg-gray-50">
			<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
				<div className="text-center mb-16">
					<h2 className="text-3xl md:text-4xl font-display font-bold text-gray-900 mb-4">
						{t("howItWorks.title")}
					</h2>
					<p className="text-lg text-gray-600 max-w-2xl mx-auto">
						{t("howItWorks.subtitle")}
					</p>
				</div>

				<div className="grid grid-cols-1 md:grid-cols-3 gap-8">
					{steps.map((step, index) => {
						const Icon = step.icon;
						return (
							<div key={index} className="text-center group">
								<div className="relative mb-8">
									<div className="w-20 h-20 bg-teal-500 rounded-full flex items-center justify-center mx-auto shadow-lg group-hover:scale-110 transition-transform">
										<Icon className="w-10 h-10 text-white" />
									</div>
									<div className="absolute -top-2 -right-2 w-8 h-8 bg-coral-400 text-white rounded-full flex items-center justify-center text-sm font-bold">
										{index + 1}
									</div>
								</div>
								<h3 className="text-xl font-display font-semibold text-gray-900 mb-4">
									{step.title}
								</h3>
								<p className="text-gray-600 max-w-sm mx-auto">
									{step.description}
								</p>
							</div>
						);
					})}
				</div>
			</div>
		</section>
	);
}
