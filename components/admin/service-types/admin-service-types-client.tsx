"use client";

import { useState } from "react";
import { useTranslations } from "next-intl";
import {
	Plus,
	Search,
	Filter,
	Edit,
	Trash2,
	Eye,
	<PERSON>Off,
	Loader2,
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "@/components/ui/table";
import { ServiceTypeCreateDialog } from "./service-type-create-dialog";
import { ServiceTypeEditDialog } from "./service-type-edit-dialog";
import { ServiceTypeDeleteDialog } from "./service-type-delete-dialog";
import { toast } from "sonner";
import type { ServiceTypeWithCreator } from "@/lib/types/clinic";
import { api } from "@/lib/trpc/react";

export function AdminServiceTypesClient() {
	const t = useTranslations("admin.serviceTypes");
	const [searchTerm, setSearchTerm] = useState("");
	const [categoryFilter, setCategoryFilter] = useState<string>("");
	const [activeFilter, setActiveFilter] = useState<string>("");
	const [showCreateDialog, setShowCreateDialog] = useState(false);
	const [editingServiceType, setEditingServiceType] =
		useState<ServiceTypeWithCreator | null>(null);
	const [deletingServiceType, setDeletingServiceType] =
		useState<ServiceTypeWithCreator | null>(null);

	// Fetch service types
	const {
		data: serviceTypes,
		isLoading,
		refetch,
	} = api.serviceTypes.adminGetAll.useQuery({
		search: searchTerm || undefined,
		category: categoryFilter || undefined,
		isActive:
			activeFilter === "active"
				? true
				: activeFilter === "inactive"
					? false
					: undefined,
		limit: 50,
	});

	// Fetch categories for filter
	const { data: categories } = api.serviceTypes.getCategories.useQuery();

	// Toggle active status mutation
	const toggleActiveMutation = api.serviceTypes.toggleActive.useMutation({
		onSuccess: () => {
			toast.success(t("toggleSuccess"));
			refetch();
		},
		onError: (error) => {
			toast.error(error.message);
		},
	});

	const handleToggleActive = async (serviceTypeId: number) => {
		try {
			await toggleActiveMutation.mutateAsync({ serviceTypeId });
		} catch (error) {
			// Error handled in onError callback
		}
	};

	const handleCreateSuccess = () => {
		setShowCreateDialog(false);
		refetch();
	};

	const handleEditSuccess = () => {
		setEditingServiceType(null);
		refetch();
	};

	const handleDeleteSuccess = () => {
		setDeletingServiceType(null);
		refetch();
	};

	if (isLoading) {
		return (
			<div className="flex justify-center items-center py-12">
				<Loader2 className="h-5 w-5 animate-spin text-gray-500" />
			</div>
		);
	}

	return (
		<div className="space-y-6">
			{/* Header Actions */}
			<div className="flex flex-col sm:flex-row gap-4 justify-between">
				<div className="flex flex-col sm:flex-row gap-4 flex-1">
					{/* Search */}
					<div className="relative flex-1 max-w-md">
						<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
						<Input
							placeholder={t("searchPlaceholder")}
							value={searchTerm}
							onChange={(e) => setSearchTerm(e.target.value)}
							className="pl-10"
						/>
					</div>

					{/* Filters */}
					<div className="flex gap-2">
						<Select
							value={categoryFilter}
							onValueChange={setCategoryFilter}
						>
							<SelectTrigger className="w-40">
								<SelectValue
									placeholder={t("filterByCategory")}
								/>
							</SelectTrigger>
							<SelectContent>
								{/* @ts-expect-error */}
								<SelectItem>{t("allCategories")}</SelectItem>
								{categories?.map((category) => (
									<SelectItem key={category} value={category}>
										{category}
									</SelectItem>
								))}
							</SelectContent>
						</Select>

						<Select
							value={activeFilter}
							onValueChange={setActiveFilter}
						>
							<SelectTrigger className="w-32">
								<SelectValue
									placeholder={t("filterByStatus")}
								/>
							</SelectTrigger>
							<SelectContent>
								{/* @ts-expect-error */}
								<SelectItem>{t("allStatuses")}</SelectItem>
								<SelectItem value="active">
									{t("active")}
								</SelectItem>
								<SelectItem value="inactive">
									{t("inactive")}
								</SelectItem>
							</SelectContent>
						</Select>
					</div>
				</div>

				{/* Create Button */}
				<Button onClick={() => setShowCreateDialog(true)}>
					<Plus className="h-4 w-4 mr-2" />
					{t("createServiceType")}
				</Button>
			</div>

			{/* Service Types Table */}
			<Card>
				<CardHeader>
					<CardTitle>{t("serviceTypesList")}</CardTitle>
				</CardHeader>
				<CardContent>
					{serviceTypes && serviceTypes.length > 0 ? (
						<Table>
							<TableHeader>
								<TableRow>
									<TableHead>{t("name")}</TableHead>
									<TableHead>{t("category")}</TableHead>
									<TableHead>{t("description")}</TableHead>
									<TableHead>{t("status")}</TableHead>
									<TableHead>{t("appointment")}</TableHead>
									<TableHead>{t("createdBy")}</TableHead>
									<TableHead className="text-right">
										{t("actions")}
									</TableHead>
								</TableRow>
							</TableHeader>
							<TableBody>
								{serviceTypes.map((serviceType) => (
									<TableRow key={serviceType.id}>
										<TableCell className="font-medium">
											{serviceType.name}
										</TableCell>
										<TableCell>
											<Badge variant="secondary">
												{serviceType.category}
											</Badge>
										</TableCell>
										<TableCell className="max-w-xs truncate">
											{serviceType.description}
										</TableCell>
										<TableCell>
											<Badge
												variant={
													serviceType.isActive
														? "default"
														: "secondary"
												}
											>
												{serviceType.isActive
													? t("active")
													: t("inactive")}
											</Badge>
										</TableCell>
										<TableCell>
											{serviceType.requiresAppointment
												? t("required")
												: t("notRequired")}
										</TableCell>
										<TableCell>
											{serviceType.createdBy.name}
										</TableCell>
										<TableCell className="text-right">
											<div className="flex justify-end gap-2">
												<Button
													variant="ghost"
													size="sm"
													onClick={() =>
														handleToggleActive(
															serviceType.id
														)
													}
													disabled={
														toggleActiveMutation.isPending
													}
												>
													{serviceType.isActive ? (
														<EyeOff className="h-4 w-4" />
													) : (
														<Eye className="h-4 w-4" />
													)}
												</Button>
												<Button
													variant="ghost"
													size="sm"
													onClick={() =>
														setEditingServiceType(
															serviceType
														)
													}
												>
													<Edit className="h-4 w-4" />
												</Button>
												<Button
													variant="ghost"
													size="sm"
													onClick={() =>
														setDeletingServiceType(
															serviceType
														)
													}
												>
													<Trash2 className="h-4 w-4" />
												</Button>
											</div>
										</TableCell>
									</TableRow>
								))}
							</TableBody>
						</Table>
					) : (
						<div className="text-center py-12">
							<p className="text-gray-500 dark:text-gray-400">
								{t("noServiceTypes")}
							</p>
						</div>
					)}
				</CardContent>
			</Card>

			{/* Dialogs */}
			<ServiceTypeCreateDialog
				open={showCreateDialog}
				onOpenChange={setShowCreateDialog}
				onSuccess={handleCreateSuccess}
			/>

			{editingServiceType && (
				<ServiceTypeEditDialog
					serviceType={editingServiceType}
					open={!!editingServiceType}
					onOpenChange={(open) =>
						!open && setEditingServiceType(null)
					}
					onSuccess={handleEditSuccess}
				/>
			)}

			{deletingServiceType && (
				<ServiceTypeDeleteDialog
					serviceType={deletingServiceType}
					open={!!deletingServiceType}
					onOpenChange={(open) =>
						!open && setDeletingServiceType(null)
					}
					onSuccess={handleDeleteSuccess}
				/>
			)}
		</div>
	);
}
