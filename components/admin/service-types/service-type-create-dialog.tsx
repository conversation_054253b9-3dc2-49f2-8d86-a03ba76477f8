"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useTranslations } from "next-intl";
import {
	<PERSON>alog,
	DialogContent,
	DialogDescription,
	Di<PERSON>Footer,
	<PERSON>alogHeader,
	DialogTitle,
} from "@/components/ui/dialog";
import {
	Form,
	FormControl,
	FormDescription,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { toast } from "sonner";
import {
	serviceTypeCreateSchema,
	type ServiceTypeCreateInput,
	SERVICE_CATEGORIES,
} from "@/lib/types/clinic";
import { api } from "@/lib/trpc/react";

interface ServiceTypeCreateDialogProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	onSuccess: () => void;
}

export function ServiceTypeCreateDialog({
	open,
	onOpenChange,
	onSuccess,
}: ServiceTypeCreateDialogProps) {
	const t = useTranslations("admin.serviceTypes");
	const [isSubmitting, setIsSubmitting] = useState(false);

	const form = useForm<ServiceTypeCreateInput>({
		resolver: zodResolver(serviceTypeCreateSchema),
		defaultValues: {
			name: "",
			description: "",
			category: "",
			isActive: true,
			requiresAppointment: false,
			displayOrder: 0,
		},
	});

	const createMutation = api.serviceTypes.create.useMutation({
		onSuccess: () => {
			toast.success(t("createSuccess"));
			form.reset();
			onSuccess();
		},
		onError: (error) => {
			toast.error(error.message);
		},
		onSettled: () => {
			setIsSubmitting(false);
		},
	});

	const onSubmit = async (data: ServiceTypeCreateInput) => {
		setIsSubmitting(true);
		try {
			await createMutation.mutateAsync(data);
		} catch (error) {
			// Error handled in onError callback
		}
	};

	const handleClose = () => {
		if (!isSubmitting) {
			form.reset();
			onOpenChange(false);
		}
	};

	return (
		<Dialog open={open} onOpenChange={handleClose}>
			<DialogContent className="sm:max-w-[600px]">
				<DialogHeader>
					<DialogTitle>{t("createServiceType")}</DialogTitle>
					<DialogDescription>
						{t("createServiceTypeDescription")}
					</DialogDescription>
				</DialogHeader>

				<Form {...form}>
					<form
						onSubmit={form.handleSubmit(onSubmit)}
						className="space-y-6"
					>
						<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
							{/* Name */}
							<FormField
								control={form.control}
								name="name"
								render={({ field }) => (
									<FormItem>
										<FormLabel>{t("name")}</FormLabel>
										<FormControl>
											<Input
												placeholder={t(
													"namePlaceholder"
												)}
												{...field}
												disabled={isSubmitting}
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							{/* Category */}
							<FormField
								control={form.control}
								name="category"
								render={({ field }) => (
									<FormItem>
										<FormLabel>{t("category")}</FormLabel>
										<Select
											onValueChange={field.onChange}
											defaultValue={field.value}
											disabled={isSubmitting}
										>
											<FormControl>
												<SelectTrigger>
													<SelectValue
														placeholder={t(
															"selectCategory"
														)}
													/>
												</SelectTrigger>
											</FormControl>
											<SelectContent>
												{Object.entries(
													SERVICE_CATEGORIES
												).map(([key, value]) => (
													<SelectItem
														key={key}
														value={value}
													>
														{t(
															`categories.${value}`
														)}
													</SelectItem>
												))}
											</SelectContent>
										</Select>
										<FormMessage />
									</FormItem>
								)}
							/>
						</div>

						{/* Description */}
						<FormField
							control={form.control}
							name="description"
							render={({ field }) => (
								<FormItem>
									<FormLabel>{t("description")}</FormLabel>
									<FormControl>
										<Textarea
											placeholder={t(
												"descriptionPlaceholder"
											)}
											className="min-h-[100px]"
											{...field}
											disabled={isSubmitting}
										/>
									</FormControl>
									<FormDescription>
										{t("descriptionHelp")}
									</FormDescription>
									<FormMessage />
								</FormItem>
							)}
						/>

						<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
							{/* Display Order */}
							<FormField
								control={form.control}
								name="displayOrder"
								render={({ field }) => (
									<FormItem>
										<FormLabel>
											{t("displayOrder")}
										</FormLabel>
										<FormControl>
											<Input
												type="number"
												min="0"
												placeholder="0"
												{...field}
												onChange={(e) =>
													field.onChange(
														Number(e.target.value)
													)
												}
												disabled={isSubmitting}
											/>
										</FormControl>
										<FormDescription>
											{t("displayOrderHelp")}
										</FormDescription>
										<FormMessage />
									</FormItem>
								)}
							/>

							{/* Checkboxes */}
							<div className="space-y-4">
								<FormField
									control={form.control}
									name="requiresAppointment"
									render={({ field }) => (
										<FormItem className="flex flex-row items-start space-x-3 space-y-0">
											<FormControl>
												<Checkbox
													checked={field.value}
													onCheckedChange={
														field.onChange
													}
													disabled={isSubmitting}
												/>
											</FormControl>
											<div className="space-y-1 leading-none">
												<FormLabel>
													{t("requiresAppointment")}
												</FormLabel>
												<FormDescription>
													{t(
														"requiresAppointmentHelp"
													)}
												</FormDescription>
											</div>
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name="isActive"
									render={({ field }) => (
										<FormItem className="flex flex-row items-start space-x-3 space-y-0">
											<FormControl>
												<Checkbox
													checked={field.value}
													onCheckedChange={
														field.onChange
													}
													disabled={isSubmitting}
												/>
											</FormControl>
											<div className="space-y-1 leading-none">
												<FormLabel>
													{t("isActive")}
												</FormLabel>
												<FormDescription>
													{t("isActiveHelp")}
												</FormDescription>
											</div>
										</FormItem>
									)}
								/>
							</div>
						</div>

						<DialogFooter>
							<Button
								type="button"
								variant="outline"
								onClick={handleClose}
								disabled={isSubmitting}
							>
								{t("cancel")}
							</Button>
							<Button type="submit" disabled={isSubmitting}>
								{isSubmitting ? t("creating") : t("create")}
							</Button>
						</DialogFooter>
					</form>
				</Form>
			</DialogContent>
		</Dialog>
	);
}
