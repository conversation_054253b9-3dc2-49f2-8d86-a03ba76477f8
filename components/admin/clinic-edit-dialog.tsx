"use client";

import { useState, useEffect } from "react";
import { useTranslations } from "next-intl";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
	<PERSON><PERSON>,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from "@/components/ui/dialog";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { Loader2 } from "lucide-react";
import { api } from "@/lib/trpc/react";
import type { ClinicWithUser } from "@/lib/types/clinic";

// Form schema for admin clinic editing
const adminClinicEditSchema = z.object({
	name: z.string().min(2, "Clinic name must be at least 2 characters").max(100),
	address: z.string().min(5, "Address must be at least 5 characters").max(200),
	phone: z.string().min(8, "Phone number must be at least 8 characters").max(20),
	website: z.string().url("Must be a valid URL").optional().or(z.literal("")),
});

type AdminClinicEditFormData = z.infer<typeof adminClinicEditSchema>;

interface AdminClinicEditDialogProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	clinic: ClinicWithUser | null;
	onSuccess?: () => void;
}

export function AdminClinicEditDialog({
	open,
	onOpenChange,
	clinic,
	onSuccess,
}: AdminClinicEditDialogProps) {
	const t = useTranslations("admin.clinics");
	const tCommon = useTranslations("common");
	const { toast } = useToast();

	const form = useForm<AdminClinicEditFormData>({
		resolver: zodResolver(adminClinicEditSchema),
		defaultValues: {
			name: "",
			address: "",
			phone: "",
			website: "",
		},
	});

	// Update form when clinic changes
	useEffect(() => {
		if (clinic) {
			form.reset({
				name: clinic.name,
				address: clinic.address,
				phone: clinic.phone,
				website: clinic.website || "",
			});
		}
	}, [clinic, form]);

	// Mutation for updating clinic
	const updateMutation = api.clinics.adminUpdate.useMutation({
		onSuccess: () => {
			toast({
				title: tCommon("success"),
				description: t("profileUpdated"),
			});
			onOpenChange(false);
			onSuccess?.();
		},
		onError: (error) => {
			toast({
				title: tCommon("error"),
				description: error.message,
				variant: "destructive",
			});
		},
	});

	const onSubmit = (data: AdminClinicEditFormData) => {
		if (!clinic) return;

		updateMutation.mutate({
			clinicId: clinic.id,
			data: {
				name: data.name,
				address: data.address,
				phone: data.phone,
				website: data.website || null,
			},
		});
	};

	const handleClose = () => {
		onOpenChange(false);
		form.reset();
	};

	return (
		<Dialog open={open} onOpenChange={handleClose}>
			<DialogContent className="sm:max-w-[500px]">
				<DialogHeader>
					<DialogTitle>{t("dialogs.edit.title")}</DialogTitle>
					<DialogDescription>
						{t("dialogs.edit.description", { clinicName: clinic?.name })}
					</DialogDescription>
				</DialogHeader>

				<Form {...form}>
					<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
						<FormField
							control={form.control}
							name="name"
							render={({ field }) => (
								<FormItem>
									<FormLabel>{t("form.name")}</FormLabel>
									<FormControl>
										<Input
											placeholder={t("form.namePlaceholder")}
											{...field}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>

						<FormField
							control={form.control}
							name="address"
							render={({ field }) => (
								<FormItem>
									<FormLabel>{t("form.address")}</FormLabel>
									<FormControl>
										<Textarea
											placeholder={t("form.addressPlaceholder")}
											{...field}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>

						<FormField
							control={form.control}
							name="phone"
							render={({ field }) => (
								<FormItem>
									<FormLabel>{t("form.phone")}</FormLabel>
									<FormControl>
										<Input
											placeholder={t("form.phonePlaceholder")}
											{...field}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>

						<FormField
							control={form.control}
							name="website"
							render={({ field }) => (
								<FormItem>
									<FormLabel>{t("form.website")}</FormLabel>
									<FormControl>
										<Input
											placeholder={t("form.websitePlaceholder")}
											{...field}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>

						<DialogFooter>
							<Button
								type="button"
								variant="outline"
								onClick={handleClose}
								disabled={updateMutation.isPending}
							>
								{tCommon("cancel")}
							</Button>
							<Button
								type="submit"
								disabled={updateMutation.isPending}
							>
								{updateMutation.isPending && (
									<Loader2 className="mr-2 h-4 w-4 animate-spin" />
								)}
								{updateMutation.isPending
									? tCommon("updating")
									: tCommon("update")}
							</Button>
						</DialogFooter>
					</form>
				</Form>
			</DialogContent>
		</Dialog>
	);
}
