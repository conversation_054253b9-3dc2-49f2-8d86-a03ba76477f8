"use client";

import { Badge } from "@/components/ui/badge";
import { useTranslations } from "next-intl";

interface StatusBadgeProps {
	status: string;
	type?: "user" | "cat" | "application";
	className?: string;
}

export function StatusBadge({ status, type = "cat", className }: StatusBadgeProps) {
	const t = useTranslations("admin");

	const getStatusConfig = (status: string, type: string) => {
		switch (type) {
			case "user":
				switch (status) {
					case "admin":
						return {
							label: t("status.user.admin"),
							variant: "default" as const,
							className: "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200",
						};
					case "rescuer":
						return {
							label: t("status.user.rescuer"),
							variant: "secondary" as const,
							className: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",
						};
					case "clinic":
						return {
							label: t("status.user.clinic"),
							variant: "secondary" as const,
							className: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
						};
					case "adopter":
						return {
							label: t("status.user.adopter"),
							variant: "outline" as const,
							className: "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200",
						};
					default:
						return {
							label: status,
							variant: "outline" as const,
							className: "",
						};
				}

			case "cat":
				switch (status) {
					case "available":
						return {
							label: t("status.cat.available"),
							variant: "default" as const,
							className: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
						};
					case "pending":
						return {
							label: t("status.cat.pending"),
							variant: "secondary" as const,
							className: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
						};
					case "adopted":
						return {
							label: t("status.cat.adopted"),
							variant: "outline" as const,
							className: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",
						};
					case "unavailable":
						return {
							label: t("status.cat.unavailable"),
							variant: "destructive" as const,
							className: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
						};
					default:
						return {
							label: status,
							variant: "outline" as const,
							className: "",
						};
				}

			case "application":
				switch (status) {
					case "pending":
						return {
							label: t("status.application.pending"),
							variant: "secondary" as const,
							className: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
						};
					case "approved":
						return {
							label: t("status.application.approved"),
							variant: "default" as const,
							className: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
						};
					case "rejected":
						return {
							label: t("status.application.rejected"),
							variant: "destructive" as const,
							className: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
						};
					default:
						return {
							label: status,
							variant: "outline" as const,
							className: "",
						};
				}

			default:
				return {
					label: status,
					variant: "outline" as const,
					className: "",
				};
		}
	};

	const config = getStatusConfig(status, type);

	return (
		<Badge
			variant={config.variant}
			className={`${config.className} ${className}`}
		>
			{config.label}
		</Badge>
	);
}
