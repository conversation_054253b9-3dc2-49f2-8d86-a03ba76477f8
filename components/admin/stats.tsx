import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import {
	<PERSON><PERSON>hart,
	Bar,
	XAxis,
	YAxis,
	CartesianGrid,
	Tooltip,
	ResponsiveContainer,
	PieChart,
	Pie,
	Cell,
	Legend,
} from "recharts";
import { useTranslations } from "next-intl";
import { api } from "@/lib/trpc/react";
import { Loader2, AlertCircle } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import type {
	AdminStats,
	AdoptionChartData,
	CatStatusChartData,
} from "@/lib/types/admin";

const COLORS = ["#3b82f6", "#10b981", "#f59e0b", "#ef4444"];

export function AdminStats() {
	const t = useTranslations("admin.stats");
	const [activeTab, setActiveTab] = useState<"monthly" | "weekly">("monthly");

	// Fetch basic stats for cat status pie chart
	const {
		data: stats,
		isLoading: statsLoading,
		error: statsError,
	} = api.admin.getStats.useQuery();

	// Fetch adoption statistics based on active tab
	const {
		data: adoptionStats,
		isLoading: adoptionLoading,
		error: adoptionError,
	} = api.admin.getAdoptionStats.useQuery({
		period: activeTab,
		months: 12,
	});

	// Transform cat status data for pie chart
	const catStatusData: CatStatusChartData[] = stats?.catsByStatus
		? Object.entries(stats.catsByStatus).map(([status, count]) => ({
				name: t(`status.${status}`),
				value: count,
				status,
			}))
		: [];

	// Transform adoption data for bar chart
	const adoptionChartData: AdoptionChartData[] = adoptionStats?.data
		? adoptionStats.data.map((item) => {
				if (adoptionStats.period === "monthly") {
					const monthlyItem = item as {
						month: string;
						count: number;
					};
					return {
						month: monthlyItem.month,
						count: monthlyItem.count,
					};
				} else {
					const weeklyItem = item as { week: string; count: number };
					return {
						week: weeklyItem.week,
						count: weeklyItem.count,
					};
				}
			})
		: [];

	// Loading component
	const LoadingChart = () => (
		<div className="h-80 flex items-center justify-center">
			<Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
		</div>
	);

	// Error component
	const ErrorChart = ({ error }: { error: string }) => (
		<div className="h-80 flex items-center justify-center">
			<Alert className="max-w-md">
				<AlertCircle className="h-4 w-4" />
				<AlertDescription>{error}</AlertDescription>
			</Alert>
		</div>
	);

	return (
		<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
			<Card>
				<CardHeader>
					<CardTitle>{t("adoptionStatistics")}</CardTitle>
				</CardHeader>
				<CardContent>
					<Tabs
						value={activeTab}
						onValueChange={(value) =>
							setActiveTab(value as "monthly" | "weekly")
						}
					>
						<TabsList className="mb-4">
							<TabsTrigger value="monthly">
								{t("monthly")}
							</TabsTrigger>
							<TabsTrigger value="weekly">
								{t("weekly")}
							</TabsTrigger>
						</TabsList>
						<TabsContent value="monthly">
							{adoptionLoading ? (
								<LoadingChart />
							) : adoptionError ? (
								<ErrorChart error={adoptionError.message} />
							) : (
								<div className="h-80">
									<ResponsiveContainer
										width="100%"
										height="100%"
									>
										<BarChart
											data={adoptionChartData}
											margin={{
												top: 5,
												right: 30,
												left: 20,
												bottom: 5,
											}}
										>
											<CartesianGrid strokeDasharray="3 3" />
											<XAxis dataKey="month" />
											<YAxis />
											<Tooltip />
											<Bar
												dataKey="count"
												fill="#3b82f6"
											/>
										</BarChart>
									</ResponsiveContainer>
								</div>
							)}
						</TabsContent>
						<TabsContent value="weekly">
							{adoptionLoading ? (
								<LoadingChart />
							) : adoptionError ? (
								<ErrorChart error={adoptionError.message} />
							) : (
								<div className="h-80">
									<ResponsiveContainer
										width="100%"
										height="100%"
									>
										<BarChart
											data={adoptionChartData}
											margin={{
												top: 5,
												right: 30,
												left: 20,
												bottom: 5,
											}}
										>
											<CartesianGrid strokeDasharray="3 3" />
											<XAxis dataKey="week" />
											<YAxis />
											<Tooltip />
											<Bar
												dataKey="count"
												fill="#3b82f6"
											/>
										</BarChart>
									</ResponsiveContainer>
								</div>
							)}
						</TabsContent>
					</Tabs>
				</CardContent>
			</Card>

			<Card>
				<CardHeader>
					<CardTitle>{t("catStatusOverview")}</CardTitle>
				</CardHeader>
				<CardContent>
					{statsLoading ? (
						<LoadingChart />
					) : statsError ? (
						<ErrorChart error={statsError.message} />
					) : (
						<div className="h-80">
							<ResponsiveContainer width="100%" height="100%">
								<PieChart>
									<Pie
										data={catStatusData}
										cx="50%"
										cy="50%"
										labelLine={false}
										outerRadius={80}
										fill="#8884d8"
										dataKey="value"
										label={({ name, percent }) =>
											`${name} ${(percent * 100).toFixed(0)}%`
										}
									>
										{catStatusData.map((_, index) => (
											<Cell
												key={`cell-${index}`}
												fill={
													COLORS[
														index % COLORS.length
													]
												}
											/>
										))}
									</Pie>
									<Tooltip />
									<Legend />
								</PieChart>
							</ResponsiveContainer>
						</div>
					)}
				</CardContent>
			</Card>
		</div>
	);
}
