"use client";

import { useState, useRef } from "react";
import { CatSearch } from "@/components/cat-search";
import { CatFilters } from "@/components/cat-filters";
import { useQueryStates } from "nuqs";
import { catListingParsers } from "@/lib/search-params";

export function CatSearchFiltersWrapper() {
	// Centralized state management for all filter parameters
	const [filters, setFilters] = useQueryStates(catListingParsers);

	// Local search input state for immediate UI updates
	const [searchInput, setSearchInput] = useState(filters.search);

	// Debounce timeout ref
	const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);

	// Single atomic reset function that updates all parameters at once
	const handleResetAll = () => {
		setSearchInput(""); // Reset local input immediately
		setFilters({
			search: null,
			page: 1,
			gender: null, // Will use default "all"
			ageMin: null, // Will use default 0
			ageMax: null, // Will use default 20
			breedId: null,
			wilayaId: null,
			communeId: null,
			available: null, // Will use default false
			specialNeeds: null, // Will use default false
			vaccinated: null, // Will use default false
			neutered: null, // Will use default false
			sort: null, // Will use default "newest"
		});
	};

	// Handler for search input changes with debouncing
	const handleSearchChange = (value: string) => {
		setSearchInput(value); // Update UI immediately

		// Clear existing timeout
		if (debounceTimeoutRef.current) {
			clearTimeout(debounceTimeoutRef.current);
		}

		// Set new timeout for API call
		debounceTimeoutRef.current = setTimeout(() => {
			const trimmedValue = value.trim();
			setFilters({
				search: trimmedValue || null,
				page: 1, // Reset to first page when searching
			});
		}, 500);
	};

	// Handler for filter changes (preserves search and page)
	const handleFiltersChange = (newFilters: Partial<typeof filters>) => {
		setFilters({
			...newFilters,
			// Preserve current search and page values unless explicitly overridden
			search:
				newFilters.search !== undefined
					? newFilters.search
					: filters.search,
			page:
				newFilters.page !== undefined ? newFilters.page : filters.page,
		});
	};

	return (
		<div className="mb-6 space-y-4 sm:space-y-0 sm:flex sm:items-center sm:justify-between sm:gap-4">
			{/* Search Component - Full width on mobile, constrained on larger screens */}
			<div className="w-full sm:w-auto sm:flex-1 sm:max-w-md">
				<CatSearch
					search={searchInput}
					onSearchChange={handleSearchChange}
				/>
			</div>

			{/* Filters Component - Positioned appropriately for each screen size */}
			<div className="w-full sm:w-auto">
				<CatFilters
					filters={filters}
					onFiltersChange={handleFiltersChange}
					onReset={handleResetAll}
				/>
			</div>
		</div>
	);
}
