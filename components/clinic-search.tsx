"use client";

import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Search, X } from "lucide-react";
import { useTranslations } from "next-intl";
import { cn } from "@/lib/utils";

interface ClinicSearchProps {
	search: string;
	onSearchChange: (value: string) => void;
}

export function ClinicSearch({ search, onSearchChange }: ClinicSearchProps) {
	const t = useTranslations("clinics");

	// Handle input changes - call parent immediately for controlled behavior
	const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		onSearchChange(e.target.value);
	};

	const clearSearch = () => {
		onSearchChange("");
	};

	const handleKeyDown = (e: React.KeyboardEvent) => {
		if (e.key === "Enter") {
			e.preventDefault();
			onSearchChange(search);
		}
	};

	return (
		<div className="relative w-full max-w-sm sm:max-w-md lg:max-w-lg">
			{/* Search Icon - Fixed positioning with proper spacing */}
			<div className="absolute left-3 top-1/2 -translate-y-1/2 pointer-events-none z-20 flex items-center justify-center">
				<Search className="h-4 w-4 text-muted-foreground shrink-0" />
			</div>

			{/* Input Field - Mobile-first responsive design */}
			<Input
				placeholder={t("search.placeholder")}
				value={search}
				onChange={handleInputChange}
				onKeyDown={handleKeyDown}
				className={cn(
					// Base styles - mobile first
					"w-full min-w-0 h-10 text-sm",
					"pl-10 pr-12", // Space for icons
					"border border-input bg-background",
					"placeholder:text-muted-foreground",
					"focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
					"transition-all duration-200",
					"rounded-md",
					// Small screens and up
					"sm:h-11 sm:text-base sm:pr-14",
					// Medium screens and up
					"md:text-sm",
					// Ensure proper overflow handling
					"overflow-hidden text-ellipsis"
				)}
			/>

			{/* Clear Button - Only show when there's text */}
			{search && (
				<Button
					type="button"
					variant="ghost"
					size="sm"
					onClick={clearSearch}
					className={cn(
						"absolute right-1 top-1/2 -translate-y-1/2 z-20",
						"h-8 w-8 p-0 hover:bg-muted/50",
						"sm:h-9 sm:w-9"
					)}
					aria-label={t("search.clear")}
				>
					<X className="h-3 w-3 sm:h-4 sm:w-4" />
				</Button>
			)}
		</div>
	);
}
