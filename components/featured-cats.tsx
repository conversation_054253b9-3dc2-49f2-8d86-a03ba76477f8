"use client";
import { Card, CardContent } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { api } from "@/lib/trpc/react";
import { useTranslations } from "next-intl";
import { CatCard } from "@/components/cat-card";
import { useFavorites } from "@/hooks/use-favorites";

export function FeaturedCats() {
	const { data: cats, isLoading, error } = api.cats.getFeatured.useQuery();
	const t = useTranslations("cats");
	const utils = api.useUtils();

	// Use favorites hook with additional queries to invalidate
	const { favoriteMap, handleToggleFavorite, pendingFavoriteId } =
		useFavorites({
			additionalQueriesToInvalidate: [
				() => utils.cats.getFeatured.invalidate(),
			],
		});

	if (isLoading) {
		return (
			<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8 w-full max-w-6xl mx-auto">
				{Array(3)
					.fill(0)
					.map((_, i) => (
						<Card
							key={i}
							className="overflow-hidden warm-shadow rounded-2xl border-0 bg-card/80"
						>
							<Skeleton className="h-56 w-full" />
							<CardContent className="p-6">
								<Skeleton className="h-6 w-3/4 mb-3" />
								<div className="flex gap-2">
									<Skeleton className="h-6 w-16 rounded-full" />
									<Skeleton className="h-6 w-20 rounded-full" />
									<Skeleton className="h-6 w-24 rounded-full" />
								</div>
							</CardContent>
						</Card>
					))}
			</div>
		);
	}

	if (error) {
		return (
			<div className="text-center py-12 w-full max-w-6xl mx-auto">
				<div className="bg-destructive/10 border border-destructive/20 rounded-2xl p-8">
					<p className="text-destructive font-medium">
						{t("errors.failedToLoad")}
					</p>
				</div>
			</div>
		);
	}

	if (!cats || cats.length === 0) {
		return (
			<div className="text-center py-12 w-full max-w-6xl mx-auto">
				<div className="bg-muted/50 rounded-2xl p-8">
					<p className="text-muted-foreground text-lg">
						{t("errors.noFeatured")}
					</p>
				</div>
			</div>
		);
	}

	return (
		<div className="cat-grid w-full max-w-7xl mx-auto">
			{cats.map((cat) => (
				<CatCard
					key={cat.id}
					cat={{
						...cat,
						isFavorite: favoriteMap.has(cat.id),
					}}
					actions={{
						mode: "view",
						onToggleFavorite: handleToggleFavorite,
						togglePending: pendingFavoriteId === cat.id,
					}}
				/>
			))}
		</div>
	);
}
