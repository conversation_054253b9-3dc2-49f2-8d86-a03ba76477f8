"use client";

import { <PERSON> } from "@/lib/i18n/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { useTranslations } from "next-intl";
import { Search, Heart, Home } from "lucide-react";

export function HeroSection() {
	const t = useTranslations("home");
	const formsT = useTranslations("forms");

	return (
		<section className="relative gradient-hero pt-16 pb-24">
			<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
				<div className="text-center mb-12">
					<h1 className="hero-title text-gray-900 mb-6">
						{t("hero.title")}
						<span className="text-teal-500 block">
							{t("hero.subtitle")}
						</span>
					</h1>
					<p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
						{t("hero.description")}
					</p>
				</div>

				{/* Search Bar - placeholder for now */}
				<div className="mb-16">
					<div className="max-w-2xl mx-auto bg-white rounded-2xl shadow-lg p-4">
						<div className="flex items-center space-x-4">
							<Search className="w-5 h-5 text-gray-400" />
							<input
								type="text"
								placeholder={t("search.placeholder")}
								className="flex-1 outline-none text-gray-700"
							/>
							<Button className="bg-teal-500 hover:bg-teal-600 text-white px-6 py-2 rounded-xl min-h-[44px]">
								{t("search.button")}
							</Button>
						</div>
					</div>
				</div>

				{/* Hero Image with floating elements */}
				<div className="relative max-w-4xl mx-auto">
					<div className="relative rounded-3xl overflow-hidden shadow-2xl">
						<img
							src="/hero-cats.jpg"
							alt="Happy cats in a loving home"
							className="w-full h-64 md:h-96 object-cover"
							onError={(e) => {
								e.currentTarget.src = "/cat.jpeg";
							}}
						/>
						<div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
					</div>

					{/* Floating elements */}
					<div className="absolute -top-4 -left-4 bg-white p-4 rounded-2xl shadow-lg">
						<Heart className="w-8 h-8 text-coral-400 fill-current" />
					</div>
					<div className="absolute -bottom-4 -right-4 bg-white p-4 rounded-2xl shadow-lg">
						<Home className="w-8 h-8 text-teal-500" />
					</div>
				</div>
			</div>
		</section>
	);
}
