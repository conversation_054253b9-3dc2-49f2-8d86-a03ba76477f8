"use client";

import Image from "next/image";

interface SafeImageProps {
	src: string;
	alt: string;
	className?: string;
	fill?: boolean;
	width?: number;
	height?: number;
}

export function SafeImage({
	src,
	alt,
	className,
	fill = true,
	width,
	height,
}: SafeImageProps) {
	return (
		<Image
			src={src}
			alt={alt}
			fill={fill}
			width={!fill ? width : undefined}
			height={!fill ? height : undefined}
			className={className}
			onError={(e) => {
				e.currentTarget.src =
					"https://placehold.co/100x100/e2e8f0/94a3b8?text=User";
			}}
		/>
	);
}
