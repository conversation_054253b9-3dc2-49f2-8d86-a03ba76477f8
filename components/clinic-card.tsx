"use client";

import { <PERSON> } from "@/lib/i18n/navigation";
import { <PERSON>, CardContent, CardFooter } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
	MapPin,
	Phone,
	Globe,
	Clock,
	Star,
	MessageCircle,
	ExternalLink,
} from "lucide-react";
import { useTranslations, useLocale } from "next-intl";
import type { ClinicWithUser } from "@/lib/types/clinic";
import { cn } from "@/lib/utils";

interface ClinicCardProps {
	clinic: ClinicWithUser & {
		wilaya?: { name: string } | null;
		commune?: { name: string } | null;
	};
	className?: string;
}

export function ClinicCard({ clinic, className }: ClinicCardProps) {
	const t = useTranslations("clinics");
	const commonT = useTranslations("common");
	const locale = useLocale();

	// Determine text direction based on locale
	const isRTL = locale === "ar";
	const dir = isRTL ? "rtl" : "ltr";

	// Format operating hours for display
	const formatOperatingHours = () => {
		if (!clinic.operatingHours) return null;

		const hours = clinic.operatingHours as Record<string, string>;
		const today = new Date().toLocaleDateString("en-US", {
			weekday: "lowercase",
		});
		const todayHours = hours[today];

		if (todayHours) {
			return `${t("todayHours")}: ${todayHours}`;
		}

		// Show first available day
		const firstDay = Object.keys(hours).find((day) => hours[day]);
		if (firstDay && hours[firstDay]) {
			return `${commonT(`days.${firstDay}`)}: ${hours[firstDay]}`;
		}

		return null;
	};

	// Format location display
	const formatLocation = () => {
		const parts = [];
		if (clinic.commune?.name) parts.push(clinic.commune.name);
		if (clinic.wilaya?.name) parts.push(clinic.wilaya.name);
		return parts.join(", ") || clinic.address;
	};

	// Format services for display (show first 3)
	const displayServices = clinic.services?.slice(0, 3) || [];
	const remainingServices =
		(clinic.services?.length || 0) - displayServices.length;

	return (
		<Card
			className={cn(
				"group relative overflow-hidden transition-all duration-300",
				"hover:shadow-lg hover:-translate-y-1",
				"border border-border bg-card",
				className
			)}
			dir={dir}
		>
			{/* Featured Badge */}
			{clinic.featured && (
				<div className="absolute top-3 right-3 z-10">
					<Badge
						variant="secondary"
						className="bg-yellow-100 text-yellow-800 border-yellow-200"
					>
						<Star className="h-3 w-3 mr-1 fill-current" />
						{t("featured")}
					</Badge>
				</div>
			)}

			<CardContent className="p-6">
				{/* Clinic Header */}
				<div className="space-y-3 mb-4">
					<div className="flex items-start justify-between gap-3">
						<div className="min-w-0 flex-1">
							<h3 className="text-lg font-semibold line-clamp-2 group-hover:underline">
								{clinic.name}
							</h3>

							{/* Location */}
							<div className="flex items-center gap-1 text-sm text-muted-foreground mt-1">
								<MapPin className="h-4 w-4 shrink-0" />
								<span className="line-clamp-1">
									{formatLocation()}
								</span>
							</div>
						</div>
					</div>

					{/* Contact Info */}
					<div className="space-y-2">
						{/* Phone */}
						<div className="flex items-center gap-2 text-sm">
							<Phone className="h-4 w-4 text-muted-foreground shrink-0" />
							<a
								href={`tel:${clinic.phone}`}
								className="text-muted-foreground hover:text-primary transition-colors"
							>
								{clinic.phone}
							</a>
						</div>

						{/* Website */}
						{clinic.website && (
							<div className="flex items-center gap-2 text-sm">
								<Globe className="h-4 w-4 text-muted-foreground shrink-0" />
								<a
									href={clinic.website}
									target="_blank"
									rel="noopener noreferrer"
									className="text-muted-foreground hover:text-primary transition-colors line-clamp-1"
								>
									{clinic.website.replace(/^https?:\/\//, "")}
									<ExternalLink className="h-3 w-3 inline ml-1" />
								</a>
							</div>
						)}

						{/* Operating Hours */}
						{formatOperatingHours() && (
							<div className="flex items-center gap-2 text-sm">
								<Clock className="h-4 w-4 text-muted-foreground shrink-0" />
								<span className="text-muted-foreground">
									{formatOperatingHours()}
								</span>
							</div>
						)}
					</div>
				</div>

				{/* Services */}
				{displayServices.length > 0 && (
					<div className="space-y-2">
						<h4 className="text-sm font-medium text-muted-foreground">
							{t("services")}
						</h4>
						<div className="flex flex-wrap gap-1">
							{displayServices.map((service, index) => (
								<Badge
									key={index}
									variant="outline"
									className="text-xs"
								>
									{service}
								</Badge>
							))}
							{remainingServices > 0 && (
								<Badge variant="outline" className="text-xs">
									+{remainingServices} {t("more")}
								</Badge>
							)}
						</div>
					</div>
				)}
			</CardContent>

			<CardFooter className="p-6 pt-0 flex gap-2">
				<Button asChild variant="default" size="sm" className="flex-1">
					<Link href={`/clinics/${clinic.slug}`}>
						{t("viewProfile")}
					</Link>
				</Button>

				<Button asChild variant="outline" size="sm">
					<Link href={`/messages/new?clinic=${clinic.slug}`}>
						<MessageCircle className="h-4 w-4" />
					</Link>
				</Button>
			</CardFooter>
		</Card>
	);
}
