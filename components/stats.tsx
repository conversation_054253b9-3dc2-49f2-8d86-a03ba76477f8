"use client";

import type React from "react";

import { useEffect, useState } from "react";
import { api } from "@/lib/trpc/react";
import { Card, CardContent } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Heart, Home, Users, Building } from "lucide-react";
import { useTranslations } from "next-intl";

export function Stats() {
	const { data, isLoading, error } = api.stats.getOverview.useQuery();
	const t = useTranslations();
	const homeT = useTranslations("home");
	const catsT = useTranslations("cats");
	const statsT = useTranslations("stats");

	if (isLoading) {
		return (
			<div className="grid grid-cols-2 md:grid-cols-4 gap-4 lg:gap-6 w-full max-w-5xl mx-auto">
				{Array(4)
					.fill(0)
					.map((_, i) => (
						<Card
							key={i}
							className="warm-shadow rounded-2xl border-0 bg-card/80"
						>
							<CardContent className="p-6 text-center">
								<Skeleton className="h-12 w-12 rounded-full mx-auto mb-4" />
								<Skeleton className="h-8 w-16 mx-auto mb-2" />
								<Skeleton className="h-4 w-full" />
							</CardContent>
						</Card>
					))}
			</div>
		);
	}

	if (error) {
		return (
			<div className="text-center py-8 w-full max-w-5xl mx-auto">
				<div className="bg-destructive/10 border border-destructive/20 rounded-2xl p-6">
					<p className="text-destructive font-medium">
						{statsT("errors.failedToLoad")}
					</p>
				</div>
			</div>
		);
	}

	if (!data) {
		return (
			<div className="text-center py-8 w-full max-w-5xl mx-auto">
				<div className="bg-muted/50 rounded-2xl p-6">
					<p className="text-muted-foreground">
						{statsT("errors.noStats")}
					</p>
				</div>
			</div>
		);
	}

	return (
		<div className="grid grid-cols-2 md:grid-cols-4 gap-4 lg:gap-6 w-full max-w-5xl mx-auto">
			<StatCard
				icon={<Home className="h-10 w-10 text-primary" />}
				value={data.adoptedCats}
				label={homeT("stats.adoptions")}
			/>
			<StatCard
				icon={<Heart className="h-10 w-10 text-primary" />}
				value={data.availableCats}
				label={catsT("status.available")}
			/>
			<StatCard
				icon={<Users className="h-10 w-10 text-primary" />}
				value={data.registeredUsers}
				label={statsT("users")}
			/>
			<StatCard
				icon={<Building className="h-10 w-10 text-primary" />}
				value={data.clinics}
				label={homeT("stats.clinics")}
			/>
		</div>
	);
}

function StatCard({
	icon,
	value,
	label,
}: {
	icon: React.ReactNode;
	value: number;
	label: string;
}) {
	const [count, setCount] = useState(0);

	useEffect(() => {
		const duration = 2000;
		const steps = 20;
		const stepValue = value / steps;
		const stepTime = duration / steps;

		let current = 0;
		const timer = setInterval(() => {
			current += stepValue;
			if (current > value) {
				setCount(value);
				clearInterval(timer);
			} else {
				setCount(Math.floor(current));
			}
		}, stepTime);

		return () => clearInterval(timer);
	}, [value]);

	return (
		<Card className="warm-shadow rounded-2xl border-0 bg-card/80 backdrop-blur-sm card-hover">
			<CardContent className="p-6 text-center">
				<div className="flex justify-center mb-4 p-3 bg-primary/10 rounded-full w-fit mx-auto">
					{icon}
				</div>
				<div className="text-2xl lg:text-3xl font-bold mb-2 text-foreground">
					{count.toLocaleString()}
				</div>
				<p className="text-muted-foreground text-sm font-medium">
					{label}
				</p>
			</CardContent>
		</Card>
	);
}
