"use client";

import { cn } from "@/lib/utils";
import { usePathname, useRouter } from "@/lib/i18n/navigation";
import { locales, type Locale } from "@/lib/i18n/routing";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { useTranslations, useLocale } from "next-intl";
import { languageConfig } from "@/lib/config/navbar-config";

export function LanguageSwitcherPopup() {
	const pathName = usePathname();
	const router = useRouter();
	const currentLocale = useLocale();
	const tLang = useTranslations("language");
	const isRTL = currentLocale === "ar";

	const switchLanguage = (locale: Locale) => {
		if (locale === currentLocale) return;
		router.replace(pathName, { locale });
	};

	return (
		<DropdownMenu>
			<DropdownMenuTrigger asChild>
				<Button
					variant="ghost"
					className={cn(
						"h-10 px-3 cursor-pointer flex items-center gap-1",
						isRTL && "flex-row-reverse"
					)}
				>
					<span
						className="text-lg"
						role="img"
						aria-label={
							languageConfig.names[currentLocale as Locale]
						}
					>
						{languageConfig.flags[currentLocale as Locale]}
					</span>
					<span className="text-xs font-medium text-muted-foreground uppercase">
						{currentLocale}
					</span>
					<span className="sr-only">{tLang("switchLanguage")}</span>
				</Button>
			</DropdownMenuTrigger>
			<DropdownMenuContent
				className={cn("w-56", isRTL && "rtl")}
				align="end"
			>
				<div className="p-2">
					<p
						className={cn(
							"text-sm font-medium mb-2 px-2 text-foreground",
							isRTL ? "text-right" : "text-left"
						)}
					>
						{tLang("switchLanguage")}
					</p>
					{locales.map((locale) => (
						<DropdownMenuItem
							key={locale}
							className={cn(
								"flex items-center gap-3 px-3 py-2 cursor-pointer",
								locale === currentLocale &&
									"bg-accent text-accent-foreground",
								isRTL && "flex-row-reverse"
							)}
							onClick={() => switchLanguage(locale)}
						>
							<span className="text-lg">
								{languageConfig.flags[locale]}
							</span>
							<span>{languageConfig.names[locale]}</span>
							{locale === currentLocale && (
								<div
									className={cn(
										"h-2 w-2 bg-primary rounded-full",
										isRTL ? "mr-auto" : "ml-auto"
									)}
								/>
							)}
						</DropdownMenuItem>
					))}
				</div>
			</DropdownMenuContent>
		</DropdownMenu>
	);
}
