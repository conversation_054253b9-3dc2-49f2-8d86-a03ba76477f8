"use client";

import { cn } from "@/lib/utils";
import { Link, usePathname, useRouter } from "@/lib/i18n/navigation";
import { locales } from "@/lib/i18n/routing";
import {
	Sheet,
	SheetContent,
	SheetTitle,
	SheetTrigger,
} from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import { Menu } from "lucide-react";
import { useTheme } from "next-themes";
import { useTranslations, useLocale } from "next-intl";
import {
	languageConfig,
	getThemeOptions,
	type NavRoute,
} from "@/lib/config/navbar-config";

interface MobileNavigationProps {
	routes: NavRoute[];
	isOpen: boolean;
	onOpenChange: (open: boolean) => void;
	user: any;
	isRTL: boolean;
}

export function MobileNavigation({
	routes,
	isOpen,
	onOpenChange,
	user,
	isRTL,
}: MobileNavigationProps) {
	const pathname = usePathname();
	const router = useRouter();
	const currentLocale = useLocale();
	const { theme, setTheme } = useTheme();
	const t = useTranslations("common");
	const tLang = useTranslations("language");
	const tTheme = useTranslations("theme");

	// Get configuration from centralized config
	const themes = getThemeOptions(tTheme);

	return (
		<Sheet open={isOpen} onOpenChange={onOpenChange}>
			<SheetTrigger asChild>
				<Button
					variant="ghost"
					size="icon"
					className="md:hidden h-10 w-10"
				>
					<Menu className="h-5 w-5" />
				</Button>
			</SheetTrigger>
			<SheetContent side={isRTL ? "left" : "right"} className="w-80">
				<SheetTitle>🐱 {t("appName")}</SheetTitle>
				<div className="flex flex-col h-full">
					{/* Mobile Navigation */}
					<div className="flex-1 py-6">
						<nav className="space-y-2">
							{routes.map((route) => (
								<Link
									key={route.href}
									href={route.href}
									className={cn(
										"flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors min-h-[44px]",
										pathname === route.href
											? "bg-accent text-accent-foreground"
											: "text-foreground hover:bg-accent/50",
										isRTL && "text-right"
									)}
									onClick={() => onOpenChange(false)}
								>
									{route.label}
								</Link>
							))}
						</nav>

						{/* Mobile Controls */}
						<div className="mt-8 space-y-4">
							<div className="px-4">
								<p
									className={cn(
										"text-sm font-medium mb-3 text-foreground",
										isRTL ? "text-right" : "text-left"
									)}
								>
									{tLang("switchLanguage")}
								</p>
								<div className="space-y-2">
									{locales.map((locale) => (
										<button
											key={locale}
											className={cn(
												"flex items-center gap-3 w-full px-3 py-2 text-sm rounded-lg transition-colors min-h-[44px]",
												locale === currentLocale
													? "bg-accent text-accent-foreground"
													: "text-foreground hover:bg-accent/50",
												isRTL &&
													"flex-row-reverse text-right"
											)}
											onClick={() => {
												router.replace(pathname, {
													locale,
												});
												onOpenChange(false);
											}}
										>
											<span className="text-lg">
												{languageConfig.flags[locale]}
											</span>
											<span>
												{languageConfig.names[locale]}
											</span>
										</button>
									))}
								</div>
							</div>

							<div className="px-4">
								<p
									className={cn(
										"text-sm font-medium mb-3 text-foreground",
										isRTL ? "text-right" : "text-left"
									)}
								>
									{tTheme("toggle")}
								</p>
								<div className="space-y-2">
									{themes.map((themeOption) => {
										const Icon = themeOption.icon;
										return (
											<button
												key={themeOption.key}
												className={cn(
													"flex items-center gap-3 w-full px-3 py-2 text-sm rounded-lg transition-colors min-h-[44px]",
													theme === themeOption.key
														? "bg-accent text-accent-foreground"
														: "text-foreground hover:bg-accent/50",
													isRTL &&
														"flex-row-reverse text-right"
												)}
												onClick={() => {
													setTheme(themeOption.key);
													onOpenChange(false);
												}}
											>
												<Icon className="h-4 w-4" />
												<span>{themeOption.label}</span>
											</button>
										);
									})}
								</div>
							</div>
						</div>
					</div>

					{/* Mobile Auth Actions */}
					{!user && (
						<div className="border-t border-border pt-4 space-y-2">
							<Button
								variant="ghost"
								size="sm"
								asChild
								className={cn(
									"w-full min-h-[44px]",
									isRTL ? "justify-end" : "justify-start"
								)}
							>
								<Link
									href="/auth/login"
									onClick={() => onOpenChange(false)}
								>
									{t("navigation.login")}
								</Link>
							</Button>
							<Button
								size="sm"
								asChild
								className="w-full min-h-[44px]"
							>
								<Link
									href="/auth/register"
									onClick={() => onOpenChange(false)}
								>
									{t("navigation.register")}
								</Link>
							</Button>
						</div>
					)}
				</div>
			</SheetContent>
		</Sheet>
	);
}
