"use client";

import { cn } from "@/lib/utils";
import { But<PERSON> } from "@/components/ui/button";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Sun, Moon } from "lucide-react";
import { useTheme } from "next-themes";
import { useTranslations, useLocale } from "next-intl";
import { getThemeOptions } from "@/lib/config/navbar-config";

export function ThemeSwitcherPopup() {
	const { setTheme, theme } = useTheme();
	const tTheme = useTranslations("theme");
	const currentLocale = useLocale();
	const isRTL = currentLocale === "ar";

	const themes = getThemeOptions(tTheme);

	return (
		<DropdownMenu>
			<DropdownMenuTrigger asChild>
				<Button
					variant="ghost"
					size="icon"
					className="h-10 w-10 cursor-pointer"
				>
					<Sun className="h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
					<Moon className="absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
					<span className="sr-only">{tTheme("toggle")}</span>
				</Button>
			</DropdownMenuTrigger>
			<DropdownMenuContent
				className={cn("w-48", isRTL && "rtl")}
				align="end"
			>
				<div className="p-2">
					<p
						className={cn(
							"text-sm font-medium mb-2 px-2 text-foreground",
							isRTL ? "text-right" : "text-left"
						)}
					>
						{tTheme("toggle")}
					</p>
					{themes.map((themeOption) => {
						const Icon = themeOption.icon;
						return (
							<DropdownMenuItem
								key={themeOption.key}
								className={cn(
									"flex items-center gap-3 px-3 py-2 cursor-pointer",
									theme === themeOption.key &&
										"bg-accent text-accent-foreground",
									isRTL && "flex-row-reverse"
								)}
								onClick={() => setTheme(themeOption.key)}
							>
								<Icon className="h-4 w-4" />
								<span>{themeOption.label}</span>
								{theme === themeOption.key && (
									<div
										className={cn(
											"h-2 w-2 bg-primary rounded-full",
											isRTL ? "mr-auto" : "ml-auto"
										)}
									/>
								)}
							</DropdownMenuItem>
						);
					})}
				</div>
			</DropdownMenuContent>
		</DropdownMenu>
	);
}
