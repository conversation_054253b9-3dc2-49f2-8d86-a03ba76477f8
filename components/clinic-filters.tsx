"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
} from "@/components/ui/dialog";
import { Checkbox } from "@/components/ui/checkbox";
import { Filter, X, MapPin, Briefcase, Star } from "lucide-react";
import { useTranslations } from "next-intl";
import { api } from "@/lib/trpc/react";
import type { ClinicListingSearchParams } from "@/lib/search-params";

interface ClinicFiltersProps {
	filters: ClinicListingSearchParams;
	onFiltersChange: (filters: Partial<ClinicListingSearchParams>) => void;
	onReset: () => void;
}

export function ClinicFilters({
	filters,
	onFiltersChange,
	onReset,
}: ClinicFiltersProps) {
	const [dialogOpen, setDialogOpen] = useState(false);
	const t = useTranslations("clinics");
	const commonT = useTranslations("common");

	// Fetch wilayas and communes using tRPC
	const { data: wilayas, isLoading: isLoadingWilayas } =
		api.location.getWilayas.useQuery();
	const [selectedWilayaId, setSelectedWilayaId] = useState<
		string | undefined
	>(filters.wilayaId || undefined);
	const { data: communes, isLoading: isLoadingCommunes } =
		api.location.getCommunesByWilaya.useQuery(
			{
				wilayaId: selectedWilayaId
					? parseInt(selectedWilayaId)
					: undefined,
			},
			{ enabled: !!selectedWilayaId }
		);

	// Fetch available service types for filtering
	const { data: serviceTypes, isLoading: isLoadingServices } =
		api.serviceTypes.getAll.useQuery();

	// Handle wilaya change
	const handleWilayaChange = (value: string) => {
		setSelectedWilayaId(value === "all" ? undefined : value);
		onFiltersChange({
			wilayaId: value === "all" ? "all" : value,
			communeId: "all", // Reset commune when wilaya changes
		});
	};

	// Handle commune change
	const handleCommuneChange = (value: string) => {
		onFiltersChange({
			communeId: value,
		});
	};

	// Handle featured filter change
	const handleFeaturedChange = (checked: boolean) => {
		onFiltersChange({
			featured: checked,
		});
	};

	// Handle service filter change
	const handleServiceChange = (serviceId: string, checked: boolean) => {
		const currentServices = filters.services || [];
		const newServices = checked
			? [...currentServices, serviceId]
			: currentServices.filter(id => id !== serviceId);
		
		onFiltersChange({
			services: newServices,
		});
	};

	// Handle sort change
	const handleSortChange = (value: string) => {
		onFiltersChange({
			sort: value as ClinicListingSearchParams["sort"],
		});
	};

	// Count active filters
	const activeFiltersCount = [
		filters.wilayaId && filters.wilayaId !== "all",
		filters.communeId && filters.communeId !== "all",
		filters.featured,
		filters.services && filters.services.length > 0,
	].filter(Boolean).length;

	return (
		<div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
			{/* Desktop Filters */}
			<div className="hidden lg:flex items-center gap-4 flex-wrap">
				{/* Location Filter */}
				<div className="flex items-center gap-2">
					<MapPin className="h-4 w-4 text-muted-foreground" />
					<Select
						value={filters.wilayaId || "all"}
						onValueChange={handleWilayaChange}
						disabled={isLoadingWilayas}
					>
						<SelectTrigger className="w-40">
							<SelectValue placeholder={t("filters.selectWilaya")} />
						</SelectTrigger>
						<SelectContent>
							<SelectItem value="all">{commonT("any")}</SelectItem>
							{wilayas?.map((wilaya) => (
								<SelectItem key={wilaya.id} value={wilaya.id.toString()}>
									{wilaya.name}
								</SelectItem>
							))}
						</SelectContent>
					</Select>

					{selectedWilayaId && (
						<Select
							value={filters.communeId || "all"}
							onValueChange={handleCommuneChange}
							disabled={isLoadingCommunes}
						>
							<SelectTrigger className="w-40">
								<SelectValue placeholder={t("filters.selectCommune")} />
							</SelectTrigger>
							<SelectContent>
								<SelectItem value="all">{commonT("any")}</SelectItem>
								{communes?.map((commune) => (
									<SelectItem key={commune.id} value={commune.id.toString()}>
										{commune.name}
									</SelectItem>
								))}
							</SelectContent>
						</Select>
					)}
				</div>

				{/* Featured Filter */}
				<div className="flex items-center gap-2">
					<Checkbox
						id="featured"
						checked={filters.featured}
						onCheckedChange={handleFeaturedChange}
					/>
					<label htmlFor="featured" className="text-sm flex items-center gap-1">
						<Star className="h-4 w-4" />
						{t("filters.featuredOnly")}
					</label>
				</div>
			</div>

			{/* Mobile Filter Dialog */}
			<div className="lg:hidden">
				<Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
					<DialogTrigger asChild>
						<Button variant="outline" size="sm">
							<Filter className="h-4 w-4 mr-2" />
							{t("filters.title")}
							{activeFiltersCount > 0 && (
								<Badge variant="secondary" className="ml-2 h-5 w-5 p-0 text-xs">
									{activeFiltersCount}
								</Badge>
							)}
						</Button>
					</DialogTrigger>
					<DialogContent className="sm:max-w-md">
						<DialogHeader>
							<DialogTitle>{t("filters.title")}</DialogTitle>
							<DialogDescription>
								{t("filters.description")}
							</DialogDescription>
						</DialogHeader>
						
						<div className="space-y-6">
							{/* Location Filters */}
							<div className="space-y-3">
								<h4 className="font-medium flex items-center gap-2">
									<MapPin className="h-4 w-4" />
									{t("filters.location")}
								</h4>
								<div className="space-y-2">
									<Select
										value={filters.wilayaId || "all"}
										onValueChange={handleWilayaChange}
										disabled={isLoadingWilayas}
									>
										<SelectTrigger>
											<SelectValue placeholder={t("filters.selectWilaya")} />
										</SelectTrigger>
										<SelectContent>
											<SelectItem value="all">{commonT("any")}</SelectItem>
											{wilayas?.map((wilaya) => (
												<SelectItem key={wilaya.id} value={wilaya.id.toString()}>
													{wilaya.name}
												</SelectItem>
											))}
										</SelectContent>
									</Select>

									{selectedWilayaId && (
										<Select
											value={filters.communeId || "all"}
											onValueChange={handleCommuneChange}
											disabled={isLoadingCommunes}
										>
											<SelectTrigger>
												<SelectValue placeholder={t("filters.selectCommune")} />
											</SelectTrigger>
											<SelectContent>
												<SelectItem value="all">{commonT("any")}</SelectItem>
												{communes?.map((commune) => (
													<SelectItem key={commune.id} value={commune.id.toString()}>
														{commune.name}
													</SelectItem>
												))}
											</SelectContent>
										</Select>
									)}
								</div>
							</div>

							{/* Featured Filter */}
							<div className="space-y-3">
								<h4 className="font-medium flex items-center gap-2">
									<Star className="h-4 w-4" />
									{t("filters.featured")}
								</h4>
								<div className="flex items-center gap-2">
									<Checkbox
										id="featured-mobile"
										checked={filters.featured}
										onCheckedChange={handleFeaturedChange}
									/>
									<label htmlFor="featured-mobile" className="text-sm">
										{t("filters.featuredOnly")}
									</label>
								</div>
							</div>

							{/* Services Filter */}
							{serviceTypes && serviceTypes.length > 0 && (
								<div className="space-y-3">
									<h4 className="font-medium flex items-center gap-2">
										<Briefcase className="h-4 w-4" />
										{t("filters.services")}
									</h4>
									<div className="space-y-2 max-h-40 overflow-y-auto">
										{serviceTypes.map((service) => (
											<div key={service.id} className="flex items-center gap-2">
												<Checkbox
													id={`service-${service.id}`}
													checked={filters.services?.includes(service.id.toString()) || false}
													onCheckedChange={(checked) => 
														handleServiceChange(service.id.toString(), checked as boolean)
													}
												/>
												<label htmlFor={`service-${service.id}`} className="text-sm">
													{service.name}
												</label>
											</div>
										))}
									</div>
								</div>
							)}
						</div>

						<div className="flex gap-2 pt-4">
							<Button variant="outline" onClick={onReset} className="flex-1">
								{t("filters.reset")}
							</Button>
							<Button onClick={() => setDialogOpen(false)} className="flex-1">
								{t("filters.apply")}
							</Button>
						</div>
					</DialogContent>
				</Dialog>
			</div>

			{/* Sort and Reset */}
			<div className="flex items-center gap-2">
				<Select
					value={filters.sort}
					onValueChange={handleSortChange}
				>
					<SelectTrigger className="w-40">
						<SelectValue />
					</SelectTrigger>
					<SelectContent>
						<SelectItem value="featured">{t("sort.featured")}</SelectItem>
						<SelectItem value="newest">{t("sort.newest")}</SelectItem>
						<SelectItem value="oldest">{t("sort.oldest")}</SelectItem>
						<SelectItem value="name_asc">{t("sort.nameAsc")}</SelectItem>
						<SelectItem value="name_desc">{t("sort.nameDesc")}</SelectItem>
					</SelectContent>
				</Select>

				{activeFiltersCount > 0 && (
					<Button variant="ghost" size="sm" onClick={onReset}>
						<X className="h-4 w-4 mr-1" />
						{t("filters.reset")}
					</Button>
				)}
			</div>
		</div>
	);
}
