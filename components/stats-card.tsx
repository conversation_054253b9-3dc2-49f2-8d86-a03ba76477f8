import type React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Loader2 } from "lucide-react";
import { Link } from "@/lib/i18n/navigation";

interface StatsCardProps {
	icon: React.ReactNode;
	title: string;
	value: number;
	description: string;
	linkHref: string;
	linkText: string;
	isLoading?: boolean;
}

export function StatsCard({
	icon,
	title,
	value,
	description,
	linkHref,
	linkText,
	isLoading = false,
}: StatsCardProps) {
	return (
		<Card>
			<CardContent className="pt-6">
				<div className="flex justify-between items-start mb-2">
					<div className="p-2 bg-muted rounded-md">{icon}</div>
					{isLoading ? (
						<div className="h-9 flex items-center">
							<Loader2 className="h-5 w-5 animate-spin text-muted-foreground" />
						</div>
					) : (
						<div className="text-3xl font-bold">{value}</div>
					)}
				</div>
				<h3 className="font-medium">{title}</h3>
				<p className="text-sm text-muted-foreground mb-4">
					{description}
				</p>
				<Button variant="outline" size="sm" className="w-full" asChild>
					<Link href={linkHref}>{linkText}</Link>
				</Button>
			</CardContent>
		</Card>
	);
}
