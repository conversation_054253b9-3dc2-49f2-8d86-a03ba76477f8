"use client";

import { useState, useCallback, useRef } from "react";
import Image from "next/image";
import {
	Dialog,
	DialogClose,
	DialogContent,
	DialogTitle,
} from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
	Carousel,
	CarouselContent,
	CarouselItem,
	CarouselNext,
	CarouselPrevious,
	type CarouselApi,
} from "@/components/ui/carousel";
import { cn } from "@/lib/utils";
import { X, ZoomIn } from "lucide-react";

interface CatGalleryProps {
	images: string[];
	name: string;
}

export function CatGallery({ images, name }: CatGalleryProps) {
	// Local state management - no URL dependencies
	const [imageLoadingStates, setImageLoadingStates] = useState<
		Record<number, boolean>
	>({});
	const [mainCarouselIndex, setMainCarouselIndex] = useState(0);
	const [isDialogOpen, setIsDialogOpen] = useState(false);
	const [dialogImageIndex, setDialogImageIndex] = useState(0);
	const mainCarouselApi = useRef<CarouselApi>(null);
	const dialogCarouselApi = useRef<CarouselApi>(null);

	// Navigation functions - simplified with local state only
	const changeMainCarouselImage = useCallback((index: number) => {
		setMainCarouselIndex(index);
		if (mainCarouselApi.current) {
			mainCarouselApi.current.scrollTo(index);
		}
	}, []);

	const navigateDialogCarousel = useCallback((index: number) => {
		setDialogImageIndex(index);
	}, []);

	const openImageDialog = useCallback((index: number) => {
		setDialogImageIndex(index);
		setIsDialogOpen(true);
	}, []);

	const closeImageDialog = useCallback(() => {
		setIsDialogOpen(false);
	}, []);

	// Ensure we have at least one image
	const displayImages =
		images.length > 0 ? images : ["/placeholder.svg?height=600&width=800"];

	// Handle image loading states
	const handleImageLoad = (index: number) => {
		setImageLoadingStates((prev) => ({ ...prev, [index]: false }));
	};

	const handleImageError = (index: number) => {
		setImageLoadingStates((prev) => ({ ...prev, [index]: false }));
	};

	const handleImageLoadStart = (index: number) => {
		setImageLoadingStates((prev) => ({ ...prev, [index]: true }));
	};

	return (
		<div className="space-y-4">
			{/* Main Carousel */}
			<div className="relative">
				<Carousel
					setApi={(newApi) => {
						if (newApi) {
							mainCarouselApi.current = newApi;
							// Handle main carousel navigation to sync with thumbnails
							newApi.on("select", () => {
								const selectedIndex =
									newApi.selectedScrollSnap();
								setMainCarouselIndex(selectedIndex);
							});
						}
					}}
					className="w-full"
					opts={{
						align: "start",
						loop: true,
						startIndex: 0,
					}}
				>
					<CarouselContent>
						{displayImages.map((image, index) => (
							<CarouselItem key={index}>
								<div className="relative">
									<div
										className="relative aspect-[4/3] rounded-lg overflow-hidden cursor-pointer bg-muted group"
										onClick={() => openImageDialog(index)}
									>
										{/* Loading skeleton */}
										{imageLoadingStates[index] && (
											<div className="absolute inset-0 bg-gray-200 animate-pulse flex items-center justify-center">
												<div className="w-8 h-8 border-2 border-gray-400 border-t-transparent rounded-full animate-spin" />
											</div>
										)}
										<Image
											src={
												image ||
												"https://placehold.co/800x600/e2e8f0/94a3b8?text=Cat+Photo"
											}
											alt={`${name} photo ${index + 1}`}
											fill
											decoding="async"
											className="object-cover object-center transition-transform duration-300 group-hover:scale-105"
											style={{
												transform:
													"translate3d(0, 0, 0)",
											}}
											onLoad={() =>
												handleImageLoad(index)
											}
											onError={() =>
												handleImageError(index)
											}
											onLoadStart={() =>
												handleImageLoadStart(index)
											}
											priority={false}
											loading={"lazy"}
											sizes="(max-width: 640px) 100vw, (max-width: 1280px) 50vw, (max-width: 1536px) 33vw, 25vw"
										/>
										{/* Zoom overlay */}
										<div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300 flex items-center justify-center">
											<div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
												<div className="bg-white/90 backdrop-blur-sm rounded-full p-3">
													<ZoomIn className="h-6 w-6 text-gray-800" />
												</div>
											</div>
										</div>
									</div>
								</div>
							</CarouselItem>
						))}
					</CarouselContent>

					{/* Navigation buttons - only show if more than 1 image */}
					{displayImages.length > 1 && (
						<>
							<CarouselPrevious className="left-4 bg-white/80 backdrop-blur-sm hover:bg-white/90 border-white/20" />
							<CarouselNext className="right-4 bg-white/80 backdrop-blur-sm hover:bg-white/90 border-white/20" />
						</>
					)}
				</Carousel>

				{/* Image counter */}
				{displayImages.length > 1 && (
					<div className="absolute bottom-4 right-4 bg-black/60 text-white px-3 py-1 rounded-full text-sm font-medium">
						{mainCarouselIndex + 1} / {displayImages.length}
					</div>
				)}
			</div>

			{/* Thumbnail Navigation - only show if more than 1 image */}
			{displayImages.length > 1 && (
				<div className="flex gap-2 overflow-x-auto pb-2 scrollbar-hide">
					{displayImages.map((image, index) => (
						<button
							key={index}
							className={cn(
								"relative h-14 w-14 sm:h-16 sm:w-16 md:h-20 md:w-20 rounded-md overflow-hidden shrink-0 border-2 bg-muted transition-all duration-200",
								"hover:border-primary/50 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2",
								"min-h-[44px] min-w-[44px] touch-manipulation", // Mobile accessibility
								mainCarouselIndex === index
									? "border-primary ring-2 ring-primary/20"
									: "border-transparent"
							)}
							onClick={() => changeMainCarouselImage(index)}
							aria-label={`View photo ${index + 1} of ${name}`}
						>
							<Image
								src={
									image ||
									"https://placehold.co/100x100/e2e8f0/94a3b8?text=Photo"
								}
								alt={`${name} thumbnail ${index + 1}`}
								fill
								className="object-cover"
								style={{ transform: "translate3d(0, 0, 0)" }}
								loading="lazy"
								sizes="(max-width: 640px) 56px, (max-width: 768px) 64px, 80px"
								onError={(e) => {
									e.currentTarget.src =
										"https://placehold.co/100x100/e2e8f0/94a3b8?text=Photo";
								}}
							/>
							{/* Active indicator */}
							{mainCarouselIndex === index && (
								<div className="absolute inset-0 bg-primary/10" />
							)}
						</button>
					))}
				</div>
			)}

			{/* Full-screen Dialog with Carousel */}
			<Dialog
				open={isDialogOpen}
				onOpenChange={(open) => !open && closeImageDialog()}
			>
				<DialogContent
					className="max-w-7xl w-full h-[90vh] p-0 overflow-hidden"
					hideCloseButton={true}
				>
					<DialogTitle className="absolute top-4 left-4 z-10 bg-black/60 text-white px-4 py-2 rounded-full text-sm font-medium">
						{name} - Photo {dialogImageIndex + 1} of{" "}
						{displayImages.length}
					</DialogTitle>
					<div className="relative h-full bg-black">
						<Carousel
							setApi={(newApi) => {
								if (newApi) {
									dialogCarouselApi.current = newApi;
									// Handle dialog carousel navigation
									newApi.on("select", () => {
										const selectedIndex =
											newApi.selectedScrollSnap();
										navigateDialogCarousel(selectedIndex);
									});
									// Scroll to current image when dialog opens
									if (dialogImageIndex > 0) {
										newApi.scrollTo(dialogImageIndex);
									}
								}
							}}
							className="h-full"
							opts={{
								align: "center",
								loop: true,
								startIndex: dialogImageIndex,
							}}
						>
							<CarouselContent className="h-full">
								{displayImages.map((image, index) => (
									<CarouselItem
										key={index}
										className="h-full"
									>
										<div className="relative h-full flex items-center justify-center p-4">
											<div className="relative max-h-full max-w-full">
												<Image
													src={
														image ||
														"https://placehold.co/1200x800/e2e8f0/94a3b8?text=Cat+Photo"
													}
													alt={`${name} full size photo ${index + 1}`}
													width={1280}
													height={853}
													className="max-h-[80vh] max-w-full object-contain"
													style={{
														transform:
															"translate3d(0, 0, 0)",
													}}
													onError={(e) => {
														e.currentTarget.src =
															"https://placehold.co/1200x800/e2e8f0/94a3b8?text=Cat+Photo";
													}}
													sizes="(max-width: 640px) 100vw, (max-width: 1280px) 90vw, 1280px"
													loading="lazy"
												/>
											</div>
										</div>
									</CarouselItem>
								))}
							</CarouselContent>

							{/* Dialog Navigation - only show if more than 1 image */}
							{displayImages.length > 1 && (
								<>
									<CarouselPrevious className="left-4 bg-white/10 backdrop-blur-sm hover:bg-white/20 border-white/20 text-white" />
									<CarouselNext className="right-4 bg-white/10 backdrop-blur-sm hover:bg-white/20 border-white/20 text-white" />
								</>
							)}
						</Carousel>

						{/* Dialog Image Counter */}
						{displayImages.length > 1 && (
							<div className="absolute bottom-4 left-1/2 -translate-x-1/2 bg-black/60 text-white px-4 py-2 rounded-full text-sm font-medium">
								{dialogImageIndex + 1} / {displayImages.length}
							</div>
						)}

						{/* Close button */}
						<DialogClose asChild>
							<Button
								variant="ghost"
								size="icon"
								className="absolute top-4 right-4 bg-white pointer-cursor text-black border-white/20"
								onClick={closeImageDialog}
							>
								<X />
							</Button>
						</DialogClose>
					</div>
				</DialogContent>
			</Dialog>
		</div>
	);
}
