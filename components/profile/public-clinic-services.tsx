"use client"

import { useState, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useToast } from "@/hooks/use-toast"
import { CheckCircle, Clock } from "lucide-react"

export function PublicClinicServices({ clinicId }: { clinicId: string }) {
  const [services, setServices] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const { toast } = useToast()

  useEffect(() => {
    // Simulate API call to fetch services
    const fetchServices = async () => {
      setIsLoading(true)
      try {
        // In a real app, this would be an API call
        await new Promise((resolve) => setTimeout(resolve, 500))

        // Mock data
        const mockServices = [
          {
            id: "1",
            name: "Vaccinations",
            description:
              "Core and non-core vaccinations for cats of all ages. Includes FVRCP, rabies, and FeLV vaccines.",
            price: "$30-$60",
            isAvailable: true,
            requiresAppointment: false,
          },
          {
            id: "2",
            name: "<PERSON><PERSON>/<PERSON><PERSON><PERSON>",
            description:
              "Surgical sterilization for cats. Includes pre-surgical examination, anesthesia, and post-operative care.",
            price: "$100-$200",
            isAvailable: true,
            requiresAppointment: true,
          },
          {
            id: "3",
            name: "Microchipping",
            description: "Permanent identification for your cat. Includes microchip implantation and registration.",
            price: "$45",
            isAvailable: true,
            requiresAppointment: false,
          },
          {
            id: "4",
            name: "Dental Cleaning",
            description:
              "Professional dental cleaning for cats. Includes scaling, polishing, and examination under anesthesia.",
            price: "$200-$400",
            isAvailable: true,
            requiresAppointment: true,
          },
          {
            id: "5",
            name: "Wellness Exams",
            description:
              "Comprehensive physical examination to assess your cat's overall health and detect any issues early.",
            price: "$50-$75",
            isAvailable: true,
            requiresAppointment: true,
          },
        ]

        // Only show available services
        const availableServices = mockServices.filter((service) => service.isAvailable)
        setServices(availableServices)
      } catch (error) {
        console.error("Error fetching services:", error)
        toast({
          title: "Error",
          description: "Failed to load services. Please try again later.",
          variant: "destructive",
        })
      } finally {
        setIsLoading(false)
      }
    }

    fetchServices()
  }, [clinicId, toast])

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {[1, 2, 3, 4].map((i) => (
          <Card key={i}>
            <CardContent className="p-6">
              <div className="h-6 w-1/2 bg-muted animate-pulse mb-3" />
              <div className="h-4 w-1/4 bg-muted animate-pulse mb-4" />
              <div className="h-4 w-full bg-muted animate-pulse mb-2" />
              <div className="h-4 w-3/4 bg-muted animate-pulse" />
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  if (services.length === 0) {
    return (
      <div className="text-center py-12">
        <h3 className="text-xl font-medium mb-2">No services listed</h3>
        <p className="text-muted-foreground">This clinic hasn't added any services yet.</p>
      </div>
    )
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      {services.map((service) => (
        <Card key={service.id}>
          <CardContent className="p-6">
            <h3 className="text-xl font-bold mb-2">{service.name}</h3>

            <div className="flex flex-wrap gap-2 mb-3">
              {service.requiresAppointment ? (
                <Badge variant="outline" className="border-amber-500 text-amber-500">
                  <Clock className="h-3.5 w-3.5 mr-1" />
                  Appointment Required
                </Badge>
              ) : (
                <Badge variant="outline" className="border-green-500 text-green-500">
                  <CheckCircle className="h-3.5 w-3.5 mr-1" />
                  Walk-in Available
                </Badge>
              )}
              {service.price && <Badge variant="outline">{service.price}</Badge>}
            </div>

            <p className="text-muted-foreground">{service.description}</p>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
