"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
	Form,
	FormControl,
	FormDescription,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
	Card,
	CardContent,
	CardFooter,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { useToast } from "@/hooks/use-toast";
import { Switch } from "@/components/ui/switch";
import { Loader2 } from "lucide-react";
import { api } from "@/lib/trpc/react";
import { serviceCreateSchema, serviceUpdateSchema } from "@/lib/types/clinic";
import type {
	ClinicService,
	ServiceCreateInput,
	ServiceUpdateInput,
} from "@/lib/types/clinic";
import { useTranslations } from "next-intl";

interface ClinicServicesFormProps {
	service?: ClinicService;
	onSuccess?: () => void;
	onCancel?: () => void;
}

export function ClinicServicesForm({
	service,
	onSuccess,
	onCancel,
}: ClinicServicesFormProps) {
	const { toast } = useToast();
	const t = useTranslations("services");
	const tCommon = useTranslations("common");
	const tButtons = useTranslations("buttons");
	const isEditing = !!service;

	// Form setup with proper typing
	const form = useForm<ServiceCreateInput>({
		resolver: zodResolver(
			isEditing ? serviceUpdateSchema : serviceCreateSchema
		),
		defaultValues: {
			name: service?.name || "",
			description: service?.description || "",
			price: service?.price || "",
			isAvailable: service?.isAvailable ?? true,
			requiresAppointment: service?.requiresAppointment ?? false,
			category: service?.category || "",
			displayOrder: service?.displayOrder ?? 0,
		},
	});

	// Mutations
	const createServiceMutation = api.clinics.createService.useMutation({
		onSuccess: () => {
			toast({
				title: t("messages.serviceAdded"),
				description: t("messages.serviceAddedDescription"),
			});
			form.reset();
			onSuccess?.();
		},
		onError: (error) => {
			toast({
				title: tCommon("error"),
				description: error.message || t("messages.errorAdding"),
				variant: "destructive",
			});
		},
	});

	const updateServiceMutation = api.clinics.updateService.useMutation({
		onSuccess: () => {
			toast({
				title: t("messages.serviceUpdated"),
				description: t("messages.serviceUpdatedDescription"),
			});
			onSuccess?.();
		},
		onError: (error) => {
			toast({
				title: tCommon("error"),
				description: error.message || t("messages.errorUpdating"),
				variant: "destructive",
			});
		},
	});

	const isSubmitting =
		createServiceMutation.isPending || updateServiceMutation.isPending;

	function onSubmit(values: ServiceCreateInput) {
		if (isEditing && service) {
			updateServiceMutation.mutate({
				serviceId: service.id,
				data: values as ServiceUpdateInput,
			});
		} else {
			createServiceMutation.mutate(values);
		}
	}

	return (
		<Card>
			<CardHeader>
				<CardTitle>
					{isEditing ? t("editService") : t("addService")}
				</CardTitle>
			</CardHeader>
			<Form {...form}>
				<form onSubmit={form.handleSubmit(onSubmit)}>
					<CardContent className="space-y-4">
						<FormField
							control={form.control}
							name="name"
							render={({ field }) => (
								<FormItem>
									<FormLabel>
										{t("form.serviceName")}
									</FormLabel>
									<FormControl>
										<Input
											{...field}
											placeholder={t(
												"form.serviceNamePlaceholder"
											)}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>

						<FormField
							control={form.control}
							name="description"
							render={({ field }) => (
								<FormItem>
									<FormLabel>
										{t("form.description")}
									</FormLabel>
									<FormControl>
										<Textarea
											{...field}
											placeholder={t(
												"form.descriptionPlaceholder"
											)}
											className="resize-none"
											rows={4}
										/>
									</FormControl>
									<FormDescription>
										{t("form.descriptionHelp")}
									</FormDescription>
									<FormMessage />
								</FormItem>
							)}
						/>

						<FormField
							control={form.control}
							name="category"
							render={({ field }) => (
								<FormItem>
									<FormLabel>{t("form.category")}</FormLabel>
									<FormControl>
										<Input
											{...field}
											placeholder={t(
												"form.categoryPlaceholder"
											)}
										/>
									</FormControl>
									<FormDescription>
										{t("form.categoryHelp")}
									</FormDescription>
									<FormMessage />
								</FormItem>
							)}
						/>

						<FormField
							control={form.control}
							name="price"
							render={({ field }) => (
								<FormItem>
									<FormLabel>{t("form.price")}</FormLabel>
									<FormControl>
										<Input
											{...field}
											placeholder={t(
												"form.pricePlaceholder"
											)}
										/>
									</FormControl>
									<FormDescription>
										{t("form.priceHelp")}
									</FormDescription>
									<FormMessage />
								</FormItem>
							)}
						/>

						<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
							<FormField
								control={form.control}
								name="isAvailable"
								render={({ field }) => (
									<FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
										<div className="space-y-0.5">
											<FormLabel className="text-base">
												{t("form.available")}
											</FormLabel>
											<FormDescription>
												{t("form.availableHelp")}
											</FormDescription>
										</div>
										<FormControl>
											<Switch
												checked={field.value}
												onCheckedChange={field.onChange}
											/>
										</FormControl>
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="requiresAppointment"
								render={({ field }) => (
									<FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
										<div className="space-y-0.5">
											<FormLabel className="text-base">
												{t("form.requiresAppointment")}
											</FormLabel>
											<FormDescription>
												{t(
													"form.requiresAppointmentHelp"
												)}
											</FormDescription>
										</div>
										<FormControl>
											<Switch
												checked={field.value}
												onCheckedChange={field.onChange}
											/>
										</FormControl>
									</FormItem>
								)}
							/>
						</div>
					</CardContent>
					<CardFooter className="flex justify-between">
						<Button
							type="button"
							variant="outline"
							onClick={onCancel || (() => form.reset())}
							disabled={isSubmitting}
						>
							{tButtons("cancel")}
						</Button>
						<Button type="submit" disabled={isSubmitting}>
							{isSubmitting ? (
								<>
									<Loader2 className="h-4 w-4 mr-2 animate-spin" />
									{isEditing
										? t("form.updating")
										: t("form.adding")}
								</>
							) : isEditing ? (
								t("form.updateService")
							) : (
								t("form.addService")
							)}
						</Button>
					</CardFooter>
				</form>
			</Form>
		</Card>
	);
}
