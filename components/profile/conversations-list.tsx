"use client";

import { useState } from "react";
import { Skeleton } from "@/components/ui/skeleton";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Search, Shield } from "lucide-react";
import { api } from "@/lib/trpc/react";
import { useTranslations } from "next-intl";
import { Link, usePathname } from "@/lib/i18n/navigation";
import { cn } from "@/lib/utils";

export function ConversationsList() {
	const [searchQuery, setSearchQuery] = useState("");
	const pathname = usePathname();
	const t = useTranslations("profile.messages");

	// Fetch conversations using optimized tRPC query
	const { data: conversationsData, isLoading } =
		api.messages.getMyConversations.useQuery({ page: 1, limit: 50 });

	const conversations = conversationsData?.conversations || [];

	const filteredConversations =
		conversations?.filter(
			(conv) =>
				conv.with?.name
					.toLowerCase()
					.includes(searchQuery.toLowerCase()) ||
				(conv.cat?.name &&
					conv.cat.name
						.toLowerCase()
						.includes(searchQuery.toLowerCase())) ||
				conv.lastMessage?.text
					.toLowerCase()
					.includes(searchQuery.toLowerCase())
		) || [];

	// Helper function to format time like in the reference
	const formatTime = (timestamp: string) => {
		const date = new Date(timestamp);
		const now = new Date();
		const today = new Date(
			now.getFullYear(),
			now.getMonth(),
			now.getDate()
		);
		const messageDate = new Date(
			date.getFullYear(),
			date.getMonth(),
			date.getDate()
		);

		if (messageDate.getTime() === today.getTime()) {
			return date.toLocaleTimeString("en-US", {
				hour: "numeric",
				minute: "2-digit",
				hour12: true,
			});
		} else if (messageDate.getTime() === today.getTime() - 86400000) {
			return "Yesterday";
		} else {
			return date.toLocaleDateString("en-US", {
				month: "short",
				day: "numeric",
			});
		}
	};

	if (isLoading) {
		return <ConversationsListSkeleton />;
	}

	if (!conversations || conversations.length === 0) {
		return (
			<div className="flex flex-col items-center justify-center py-12 px-4 text-center">
				<div className="mb-4 rounded-full bg-gray-100 p-3">
					<Search className="h-6 w-6 text-gray-400" />
				</div>
				<p className="text-gray-600">
					{t("noMessagesStartConversation")}
				</p>
			</div>
		);
	}

	return (
		<div>
			{/* Search */}
			<div className="p-4 border-b border-gray-200 dark:border-gray-700">
				<div className="relative">
					<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
					<input
						type="text"
						value={searchQuery}
						onChange={(e) => setSearchQuery(e.target.value)}
						placeholder={
							t("searchPlaceholder") || "Search conversations..."
						}
						className="w-full pl-10 pr-4 py-3 border border-gray-200 dark:border-gray-700 rounded-xl bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder:text-gray-500 dark:placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent"
					/>
				</div>
			</div>

			{/* Conversations List */}
			<div className="flex-1 overflow-y-auto">
				{filteredConversations.length === 0 ? (
					<div className="flex flex-col items-center justify-center py-12 px-4 text-center">
						<p className="text-gray-600">
							{t("noMatchingConversations")}
						</p>
					</div>
				) : (
					filteredConversations.map((conversation) => {
						const isActive = pathname.includes(
							`/messages/${conversation.id}`
						);

						const isVerified =
							conversation.with?.isVerified || false;
						const unreadCount = conversation.unreadCount || 0;

						return (
							<Link
								href={`/messages/${conversation.id}`}
								key={conversation.id}
							>
								<button
									className={cn(
										"w-full p-4 cursor-pointer text-left transition-colors border-b border-gray-100 dark:border-gray-800",
										// Base hover state
										"hover:bg-gray-50 dark:hover:bg-gray-800/50",
										// Active conversation styling
										isActive &&
											"bg-teal-50 dark:bg-teal-900/20 border-r-2 border-r-teal-500",
										// Unread messages styling - subtle tint
										unreadCount > 0 &&
											!isActive &&
											"bg-red-50/50 dark:bg-blue-900/10 hover:bg-blue-50 dark:hover:bg-blue-900/20",
										// Read messages styling (default)
										unreadCount === 0 &&
											!isActive &&
											"bg-white dark:bg-gray-900"
									)}
								>
									<div className="flex items-center space-x-3">
										{/* Avatar */}
										<div className="relative flex-shrink-0">
											<Avatar className="w-12 h-12 flex items-center justify-center">
												<AvatarImage
													src={
														conversation.with
															?.image ||
														"/placeholder-user.svg"
													}
													alt={
														conversation.with
															?.name ||
														t("unknownUser")
													}
													className="object-cover w-full h-full rounded-full"
												/>
												<AvatarFallback className="w-full h-full flex items-center justify-center text-sm font-medium bg-gray-100 rounded-full">
													{conversation.with?.name
														?.charAt(0)
														?.toUpperCase() || "U"}
												</AvatarFallback>
											</Avatar>
											{unreadCount > 0 && (
												<div className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
													{unreadCount}
												</div>
											)}
										</div>

										{/* Content */}
										<div className="flex-1 min-w-0">
											<div className="flex items-center justify-between mb-1">
												<div className="flex items-center space-x-2">
													<h3
														className={cn(
															"font-semibold truncate",
															unreadCount > 0
																? "text-gray-900"
																: "text-gray-700"
														)}
													>
														{conversation.with
															?.name ||
															t("unknownUser")}
													</h3>
													{isVerified && (
														<Shield className="w-4 h-4 text-blue-500" />
													)}
												</div>
												<span className="text-xs text-gray-500 flex-shrink-0">
													{conversation.lastMessage
														? formatTime(
																conversation
																	.lastMessage
																	.timestamp
															)
														: ""}
												</span>
											</div>

											{/* Cat Reference */}
											{conversation.cat && (
												<div className="flex items-center space-x-2 mb-1">
													<Avatar className="w-6 h-6 flex items-center justify-center">
														<AvatarImage
															src={
																conversation.cat
																	.imageUrl ||
																"/cat.jpeg"
															}
															alt={
																conversation.cat
																	.name
															}
															className="object-cover w-full h-full rounded-full"
														/>
														<AvatarFallback className="w-full h-full flex items-center justify-center text-xs font-medium bg-gray-100 rounded-full">
															{conversation.cat.name
																?.charAt(0)
																?.toUpperCase() ||
																"C"}
														</AvatarFallback>
													</Avatar>
													<span className="text-xs text-teal-600 font-medium">
														{t("regarding")}{" "}
														{conversation.cat.name}
													</span>
												</div>
											)}

											{conversation.lastMessage && (
												<p
													className={cn(
														"text-sm truncate",
														unreadCount > 0
															? "text-gray-900 font-medium"
															: "text-gray-600"
													)}
												>
													{
														conversation.lastMessage
															.text
													}
												</p>
											)}
										</div>
									</div>
								</button>
							</Link>
						);
					})
				)}
			</div>
		</div>
	);
}

function ConversationsListSkeleton() {
	return (
		<div>
			{/* Search Skeleton */}
			<div className="p-4 border-b border-gray-200 dark:border-gray-700">
				<Skeleton className="h-12 w-full rounded-xl" />
			</div>

			{/* Conversation Items Skeleton */}
			<div className="flex-1 overflow-y-auto">
				{[1, 2, 3, 4, 5].map((i) => (
					<div
						key={i}
						className="p-4 border-b border-gray-100 dark:border-gray-800"
					>
						<div className="flex items-start space-x-3">
							<Skeleton className="h-12 w-12 rounded-full flex-shrink-0" />
							<div className="flex-1 min-w-0">
								<div className="flex items-center justify-between mb-1">
									<Skeleton className="h-4 w-32" />
									<Skeleton className="h-3 w-12" />
								</div>
								<div className="flex items-center space-x-2 mb-1">
									<Skeleton className="h-6 w-6 rounded-full" />
									<Skeleton className="h-3 w-20" />
								</div>
								<Skeleton className="h-3 w-full" />
							</div>
						</div>
					</div>
				))}
			</div>
		</div>
	);
}
