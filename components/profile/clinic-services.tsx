"use client";

import { useState } from "react";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardFooter } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Edit, Trash2, Plus, CheckCircle, Clock, Loader2 } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from "@/components/ui/dialog";
import { Switch } from "@/components/ui/switch";
import { api } from "@/lib/trpc/react";
import type { ClinicServiceWithType } from "@/lib/types/clinic";
import { useTranslations } from "next-intl";

export function ClinicServices() {
	const { toast } = useToast();
	const t = useTranslations("services");
	const tCommon = useTranslations("common");
	const [serviceToDelete, setServiceToDelete] =
		useState<ClinicServiceWithType | null>(null);
	const [dialogOpen, setDialogOpen] = useState(false);

	// Fetch services using tRPC
	const {
		data: services = [],
		isLoading,
		error,
		refetch,
	} = api.clinics.getOwnServices.useQuery();

	// Mutations
	const deleteServiceMutation = api.clinics.deleteService.useMutation({
		onSuccess: () => {
			toast({
				title: t("messages.serviceDeleted"),
				description: t("messages.serviceDeletedDescription"),
			});
			refetch();
			setDialogOpen(false);
			setServiceToDelete(null);
		},
		onError: (error) => {
			toast({
				title: tCommon("error"),
				description: error.message || t("messages.errorDeleting"),
				variant: "destructive",
			});
		},
	});

	const toggleAvailabilityMutation =
		api.clinics.toggleServiceAvailability.useMutation({
			onSuccess: () => {
				toast({
					title: t("messages.availabilityUpdated"),
					description: t("messages.availabilityUpdatedDescription"),
				});
				refetch();
			},
			onError: (error) => {
				toast({
					title: tCommon("error"),
					description: error.message || t("messages.errorToggling"),
					variant: "destructive",
				});
			},
		});

	const deleteService = () => {
		if (serviceToDelete) {
			deleteServiceMutation.mutate({ serviceId: serviceToDelete.id });
		}
	};

	const openDeleteDialog = (service: ClinicServiceWithType) => {
		setServiceToDelete(service);
		setDialogOpen(true);
	};

	const toggleServiceAvailability = (serviceId: number) => {
		toggleAvailabilityMutation.mutate({ serviceId });
	};

	// Loading state
	if (isLoading) {
		return (
			<div className="space-y-6">
				<div className="flex justify-between items-center">
					<div className="h-4 w-32 bg-muted animate-pulse rounded" />
					<div className="h-10 w-32 bg-muted animate-pulse rounded" />
				</div>
				<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
					{[1, 2, 3, 4].map((i) => (
						<Card key={i}>
							<CardContent className="p-6">
								<div className="h-6 w-1/2 bg-muted animate-pulse mb-3 rounded" />
								<div className="h-4 w-1/4 bg-muted animate-pulse mb-4 rounded" />
								<div className="h-4 w-full bg-muted animate-pulse mb-2 rounded" />
								<div className="h-4 w-3/4 bg-muted animate-pulse rounded" />
							</CardContent>
						</Card>
					))}
				</div>
			</div>
		);
	}

	// Error state
	if (error) {
		return (
			<div className="text-center py-12">
				<h3 className="text-xl font-medium mb-2">
					Failed to load services
				</h3>
				<p className="text-muted-foreground mb-6">
					{error.message ||
						"There was an error loading your services."}
				</p>
				<Button onClick={() => refetch()}>Try Again</Button>
			</div>
		);
	}
	// Empty services state
	if (services.length === 0) {
		return (
			<div className="text-center py-12">
				<h3 className="text-xl font-medium mb-2">
					No services listed yet
				</h3>
				<p className="text-muted-foreground mb-6">
					Add services that your clinic offers to help cat owners and
					rescuers.
				</p>
				<Button asChild>
					<Link href="/profile/services/add">
						<Plus className="h-4 w-4 mr-2" />
						Add Service
					</Link>
				</Button>
			</div>
		);
	}

	return (
		<div className="space-y-6">
			<div className="flex justify-between items-center">
				<p className="text-muted-foreground">
					Showing {services.length} services
				</p>
				<Button asChild>
					<Link href="/profile/services/add">
						<Plus className="h-4 w-4 mr-2" />
						Add Service
					</Link>
				</Button>
			</div>

			<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
				{services.map((service) => (
					<Card
						key={service.id}
						className={`${!service.isAvailable ? "opacity-75" : ""}`}
					>
						<CardContent className="p-6">
							<div className="flex justify-between items-start mb-2">
								<h3 className="text-xl font-bold">
									{service.serviceType.name}
								</h3>
								<div className="flex items-center gap-2">
									<Switch
										checked={service.isAvailable}
										onCheckedChange={() =>
											toggleServiceAvailability(
												service.id
											)
										}
										disabled={
											toggleAvailabilityMutation.isPending
										}
										aria-label="Toggle availability"
									/>
									<span className="text-sm text-muted-foreground">
										{toggleAvailabilityMutation.isPending ? (
											<Loader2 className="h-4 w-4 animate-spin" />
										) : service.isAvailable ? (
											"Available"
										) : (
											"Unavailable"
										)}
									</span>
								</div>
							</div>

							<div className="flex flex-wrap gap-2 mb-3">
								{service.serviceType.requiresAppointment ? (
									<Badge
										variant="outline"
										className="border-amber-500 text-amber-500"
									>
										<Clock className="h-3.5 w-3.5 mr-1" />
										Appointment Required
									</Badge>
								) : (
									<Badge
										variant="outline"
										className="border-green-500 text-green-500"
									>
										<CheckCircle className="h-3.5 w-3.5 mr-1" />
										Walk-in Available
									</Badge>
								)}
								{service.price && (
									<Badge variant="outline">
										{service.price}
									</Badge>
								)}
								<Badge variant="secondary">
									{service.serviceType.category}
								</Badge>
							</div>

							<p className="text-muted-foreground mb-4">
								{service.customDescription ||
									service.serviceType.description}
							</p>
						</CardContent>
						<CardFooter className="px-6 py-4 pt-0 flex gap-2">
							<Button
								asChild
								variant="outline"
								className="flex-1"
							>
								<Link
									href={`/profile/services/${service.id}/edit`}
								>
									<Edit className="h-4 w-4 mr-2" />
									Edit
								</Link>
							</Button>
							<Button
								variant="outline"
								size="icon"
								className="text-red-500 hover:text-red-600"
								onClick={() => openDeleteDialog(service)}
							>
								<Trash2 className="h-4 w-4" />
							</Button>
						</CardFooter>
					</Card>
				))}
			</div>

			<Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
				<DialogContent>
					<DialogHeader>
						<DialogTitle>Delete Service</DialogTitle>
						<DialogDescription>
							Are you sure you want to delete the{" "}
							{serviceToDelete?.serviceType.name} service? This
							action cannot be undone.
						</DialogDescription>
					</DialogHeader>
					<DialogFooter>
						<Button
							variant="outline"
							onClick={() => setDialogOpen(false)}
						>
							Cancel
						</Button>
						<Button
							variant="destructive"
							onClick={deleteService}
							disabled={deleteServiceMutation.isPending}
						>
							{deleteServiceMutation.isPending ? (
								<>
									<Loader2 className="h-4 w-4 mr-2 animate-spin" />
									Deleting...
								</>
							) : (
								"Delete Service"
							)}
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>
		</div>
	);
}
