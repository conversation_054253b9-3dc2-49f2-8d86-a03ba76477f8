"use client";

import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { MapPin, Calendar, User } from "lucide-react";
import { useTranslations, useLocale } from "next-intl";

interface PublicUserProfile {
	id: number;
	name: string;
	slug: string;
	role: "adopter" | "rescuer" | "clinic" | "admin";
	bio: string | null;
	location: string | null;
	image: string | null;
	createdAt: string | Date;
	stats: {
		catsListed: number;
		adoptions: number;
		reviews: number;
		rating: number;
	};
}

interface PublicUserAboutProps {
	user: PublicUserProfile;
}

export function PublicUserAbout({ user }: PublicUserAboutProps) {
	const t = useTranslations("profile.public.about");
	const locale = useLocale();

	// Determine text direction based on locale
	const isRTL = locale === "ar";
	const dir = isRTL ? "rtl" : "ltr";

	// Format the creation date to be used as "joined date"
	const joinedDate = new Date(user.createdAt).toLocaleDateString("en-US", {
		year: "numeric",
		month: "long",
		day: "numeric",
	});

	return (
		<div className="space-y-6" dir={dir}>
			<Card>
				<CardContent className="p-6">
					<div className="space-y-6">
						{/* Bio Section */}
						<div>
							<h3
								className={`text-lg font-semibold mb-3 flex items-center gap-2`}
							>
								<User className="h-5 w-5" />
								{t("bio")}
							</h3>
							{user.bio ? (
								<p
									className={`text-muted-foreground whitespace-pre-line leading-relaxed ${isRTL ? "text-right" : "text-left"}`}
								>
									{user.bio}
								</p>
							) : (
								<div className="text-center py-8">
									<p className="text-muted-foreground">
										{t("noBio")}
									</p>
									<p className="text-sm text-muted-foreground mt-1">
										{t("noBioDescription")}
									</p>
								</div>
							)}
						</div>

						{/* Divider */}
						<div className="border-t" />

						{/* User Details */}
						<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
							{user.location && (
								<div>
									<h4
										className={`font-medium mb-3 flex items-center gap-2`}
									>
										<MapPin className="h-4 w-4" />
										{t("location")}
									</h4>
									<p className="text-muted-foreground">
										{user.location}
									</p>
								</div>
							)}

							<div>
								<h4
									className={`font-medium mb-3 flex items-center gap-2`}
								>
									<Calendar className="h-4 w-4" />
									{t("memberSince")}
								</h4>
								<p className="text-muted-foreground">
									{joinedDate}
								</p>
							</div>
						</div>
					</div>
				</CardContent>
			</Card>

			{/* Activity Summary */}
			<Card>
				<CardContent className="p-6">
					<h3 className="text-lg font-semibold mb-4">
						{t("activitySummary")}
					</h3>
					<div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
						<div className="p-4 bg-muted/50 rounded-lg">
							<div className="text-2xl font-bold text-primary mb-1">
								{user.stats.catsListed}
							</div>
							<div className="text-sm text-muted-foreground">
								{t("stats.catsListed")}
							</div>
						</div>
						<div className="p-4 bg-muted/50 rounded-lg">
							<div className="text-2xl font-bold text-primary mb-1">
								{user.stats.adoptions}
							</div>
							<div className="text-sm text-muted-foreground">
								{t("stats.successfulAdoptions")}
							</div>
						</div>
						<div className="p-4 bg-muted/50 rounded-lg">
							<div className="text-2xl font-bold text-primary mb-1">
								{user.stats.rating.toFixed(1)}
							</div>
							<div className="text-sm text-muted-foreground">
								{t("stats.averageRating")}
							</div>
						</div>
						<div className="p-4 bg-muted/50 rounded-lg">
							<div className="text-2xl font-bold text-primary mb-1">
								{user.stats.reviews}
							</div>
							<div className="text-sm text-muted-foreground">
								{t("stats.reviewsReceived")}
							</div>
						</div>
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
