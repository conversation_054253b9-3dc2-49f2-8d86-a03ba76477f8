import { Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";

interface LoadingStateProps {
	className?: string;
	size?: "sm" | "md" | "lg";
}

export function LoadingState({ className, size = "md" }: LoadingStateProps) {
	const sizeClasses = {
		sm: "h-4 w-4",
		md: "h-8 w-8",
		lg: "h-12 w-12",
	};

	const containerClasses = {
		sm: "py-4",
		md: "py-8",
		lg: "py-16",
	};

	return (
		<div className={cn("flex justify-center", containerClasses[size], className)}>
			<Loader2 className={cn("animate-spin text-muted-foreground", sizeClasses[size])} />
		</div>
	);
}
