"use client";

import { useTranslations } from "next-intl";
import { cn } from "@/lib/utils";

interface StatItem {
	value: number;
	labelKey: string;
	translationNamespace?: string;
	color?: "teal" | "green" | "orange" | "purple" | "blue";
}

interface StatsGridProps {
	stats: StatItem[];
	className?: string;
}

const colorClasses = {
	teal: "text-teal-600",
	green: "text-green-600",
	orange: "text-orange-500",
	purple: "text-purple-600",
	blue: "text-blue-600",
};

export function StatsGrid({ stats, className }: StatsGridProps) {
	const t = useTranslations("profile");
	const catsT = useTranslations("cats");
	const messagesT = useTranslations("profile.messages");

	const getTranslation = (labelKey: string, namespace?: string) => {
		switch (namespace) {
			case "cats":
				return catsT(labelKey);
			case "messages":
				return messagesT(labelKey);
			default:
				return t(labelKey);
		}
	};

	return (
		<div className={cn("grid grid-cols-2 md:grid-cols-4 gap-4", className)}>
			{stats.map((stat, index) => (
				<div
					key={index}
					className="bg-white rounded-xl p-6 text-center shadow-sm border border-gray-200"
				>
					<div
						className={cn(
							"text-2xl font-bold",
							stat.color ? colorClasses[stat.color] : "text-teal-600"
						)}
					>
						{stat.value}
					</div>
					<div className="text-sm text-gray-600">
						{getTranslation(stat.labelKey, stat.translationNamespace)}
					</div>
				</div>
			))}
		</div>
	);
}
