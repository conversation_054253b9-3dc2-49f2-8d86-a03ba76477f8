import type React from "react";
import { LucideIcon } from "lucide-react";
import { cn } from "@/lib/utils";

interface EmptyStateProps {
	icon: LucideIcon;
	title: string;
	description: string;
	actionButton?: React.ReactNode;
	className?: string;
}

export function EmptyState({
	icon: Icon,
	title,
	description,
	actionButton,
	className,
}: EmptyStateProps) {
	return (
		<div className={cn("text-center py-16", className)}>
			<div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
				<Icon className="w-12 h-12 text-gray-400" />
			</div>
			<h3 className="text-xl font-display font-semibold text-gray-900 mb-2">
				{title}
			</h3>
			<p className="text-gray-600 mb-6">{description}</p>
			{actionButton && actionButton}
		</div>
	);
}
