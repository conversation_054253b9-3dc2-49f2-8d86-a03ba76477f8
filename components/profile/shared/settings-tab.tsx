"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useTranslations } from "next-intl";
import { Button } from "@/components/ui/button";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { api } from "@/lib/trpc/react";
import { Loader2 } from "lucide-react";
import type { User } from "@/lib/types/profile";

// Validation schema for profile updates
const profileUpdateSchema = z.object({
	name: z.string().min(2, {
		message: "Name must be at least 2 characters.",
	}),
	bio: z.string().optional(),
	wilayaId: z.string().optional(),
	communeId: z.string().optional(),
	phone: z.string().optional(),
});

type ProfileUpdateValues = z.infer<typeof profileUpdateSchema>;

interface SettingsTabProps {
	user: User;
	className?: string;
}

export function SettingsTab({ user, className }: SettingsTabProps) {
	const [isSubmitting, setIsSubmitting] = useState(false);
	const [selectedWilayaId, setSelectedWilayaId] = useState<
		string | undefined
	>(user.wilayaId ? user.wilayaId.toString() : undefined);
	const { toast } = useToast();
	const t = useTranslations("profile.settings");
	const commonT = useTranslations("common");

	// Fetch wilayas and communes using tRPC
	const { data: wilayas, isLoading: isLoadingWilayas } =
		api.location.getWilayas.useQuery();
	const { data: communes, isLoading: isLoadingCommunes } =
		api.location.getCommunesByWilaya.useQuery(
			{
				wilayaId: selectedWilayaId
					? parseInt(selectedWilayaId)
					: undefined,
			},
			{ enabled: !!selectedWilayaId }
		);

	// tRPC mutation for updating profile
	const utils = api.useUtils();
	const updateProfile = api.users.updateProfile.useMutation({
		onSuccess: () => {
			// Invalidate the user profile query to refetch the updated data
			utils.users.getProfile.invalidate();

			toast({
				title: t("profileUpdated") || "Profile updated",
				description:
					t("profileUpdatedDescription") ||
					"Your profile information has been updated.",
			});

			setIsSubmitting(false);
		},
		onError: (error) => {
			toast({
				title: commonT("error") || "Error",
				description:
					error.message ||
					t("profileUpdateError") ||
					"Your profile couldn't be updated. Please try again.",
				variant: "destructive",
			});

			setIsSubmitting(false);
		},
	});

	// Form setup with react-hook-form and zod validation
	const form = useForm<ProfileUpdateValues>({
		resolver: zodResolver(profileUpdateSchema),
		defaultValues: {
			name: user.name,
			bio: user.bio || "",
			wilayaId: user.wilayaId ? user.wilayaId.toString() : "",
			communeId: user.communeId ? user.communeId.toString() : "",
			phone: user.phone || "",
		},
	});

	// Handle wilaya change to load communes
	const handleWilayaChange = async (value: string) => {
		form.setValue("wilayaId", value);
		setSelectedWilayaId(value);
		form.setValue("communeId", ""); // Reset commune when wilaya changes
		await form.trigger(["wilayaId", "communeId"]);
	};

	// Form submission handler
	async function onSubmit(values: ProfileUpdateValues) {
		setIsSubmitting(true);

		// Use tRPC mutation to update the profile
		updateProfile.mutate({
			name: values.name,
			bio: values.bio || undefined,
			wilayaId: values.wilayaId ? parseInt(values.wilayaId) : undefined,
			communeId: values.communeId
				? parseInt(values.communeId)
				: undefined,
			phone: values.phone || undefined,
		});
	}

	return (
		<div className={`space-y-6 ${className || ""}`}>
			{/* Settings Sections */}
			<div className="space-y-6">
				{/* Profile Information */}
				<div className="bg-white border border-gray-200 rounded-2xl p-6">
					<h3 className="text-lg font-display font-semibold text-gray-900 mb-4">
						{t("profileInfo") || "Profile Information"}
					</h3>

					<Form {...form}>
						<form
							onSubmit={form.handleSubmit(onSubmit)}
							className="space-y-4"
						>
							<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
								{/* Name Field */}
								<FormField
									control={form.control}
									name="name"
									render={({ field }) => (
										<FormItem>
											<FormLabel>
												{t("name") || "Name"}
											</FormLabel>
											<FormControl>
												<Input
													{...field}
													placeholder={
														t("namePlaceholder") ||
														"Your name"
													}
													className="px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-teal-500 focus:border-transparent"
													disabled={isSubmitting}
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>

								{/* Email Field (Read-only) */}
								<div>
									<label className="block text-sm font-medium text-gray-700 mb-2">
										{t("email") || "Email"}
									</label>
									<Input
										type="email"
										value={user.email}
										className="px-4 py-3 border border-gray-300 rounded-xl bg-gray-50 text-gray-500"
										disabled
									/>
									<p className="text-xs text-gray-500 mt-1">
										{t("emailDescription") ||
											"Email address cannot be changed"}
									</p>
								</div>
							</div>

							{/* Bio Field */}
							<FormField
								control={form.control}
								name="bio"
								render={({ field }) => (
									<FormItem>
										<FormLabel>
											{t("bio") || "Bio"}
										</FormLabel>
										<FormControl>
											<Textarea
												{...field}
												rows={3}
												className="px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-teal-500 focus:border-transparent resize-none"
												placeholder={
													t("bioPlaceholder") ||
													"Tell us about yourself..."
												}
												disabled={isSubmitting}
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							{/* Location Fields - Wilaya and Commune */}
							<div className="space-y-4">
								<div className="flex items-center">
									<h4 className="text-sm font-medium text-gray-700">
										{t("location") || "Location"}
									</h4>
								</div>
								<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
									{/* Wilaya Field */}
									<FormField
										control={form.control}
										name="wilayaId"
										render={({ field }) => (
											<FormItem>
												<FormLabel>
													{t("wilaya") || "Wilaya"}
												</FormLabel>
												<Select
													onValueChange={
														handleWilayaChange
													}
													defaultValue={field.value}
													disabled={isSubmitting}
												>
													<FormControl>
														<SelectTrigger className="px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-teal-500 focus:border-transparent">
															<SelectValue
																placeholder={
																	t(
																		"wilayaPlaceholder"
																	) ||
																	"Select wilaya"
																}
															/>
														</SelectTrigger>
													</FormControl>
													<SelectContent>
														{isLoadingWilayas ? (
															<SelectItem
																value="loading"
																disabled
															>
																<Loader2 className="h-4 w-4 animate-spin mr-2 inline" />
																{t(
																	"loadingWilayas"
																) ||
																	"Loading wilayas..."}
															</SelectItem>
														) : (
															wilayas?.map(
																(
																	wilaya: any
																) => (
																	<SelectItem
																		key={
																			wilaya.id
																		}
																		value={wilaya.id.toString()}
																	>
																		{
																			wilaya.code
																		}{" "}
																		-{" "}
																		{
																			wilaya.name
																		}
																	</SelectItem>
																)
															)
														)}
													</SelectContent>
												</Select>
												<FormMessage />
											</FormItem>
										)}
									/>

									{/* Commune Field */}
									<FormField
										control={form.control}
										name="communeId"
										render={({ field }) => (
											<FormItem>
												<FormLabel>
													{t("commune") || "Commune"}
												</FormLabel>
												<Select
													onValueChange={
														field.onChange
													}
													defaultValue={field.value}
													disabled={
														isSubmitting ||
														!selectedWilayaId
													}
												>
													<FormControl>
														<SelectTrigger className="px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-teal-500 focus:border-transparent">
															<SelectValue
																placeholder={
																	selectedWilayaId
																		? t(
																				"communePlaceholder"
																			) ||
																			"Select commune"
																		: t(
																				"selectWilayaFirst"
																			) ||
																			"Select wilaya first"
																}
															/>
														</SelectTrigger>
													</FormControl>
													<SelectContent>
														{isLoadingCommunes ? (
															<SelectItem
																value="loading"
																disabled
															>
																<Loader2 className="h-4 w-4 animate-spin mr-2 inline" />
																{t(
																	"loadingCommunes"
																) ||
																	"Loading communes..."}
															</SelectItem>
														) : (
															communes?.map(
																(
																	commune: any
																) => (
																	<SelectItem
																		key={
																			commune.id
																		}
																		value={commune.id.toString()}
																	>
																		{
																			commune.name
																		}
																	</SelectItem>
																)
															)
														)}
													</SelectContent>
												</Select>
												<FormMessage />
											</FormItem>
										)}
									/>
								</div>
							</div>

							{/* Phone Field */}
							<FormField
								control={form.control}
								name="phone"
								render={({ field }) => (
									<FormItem>
										<FormLabel>
											{t("phone") || "Phone"}
										</FormLabel>
										<FormControl>
											<Input
												{...field}
												type="tel"
												placeholder={
													t("phonePlaceholder") ||
													"Your phone number"
												}
												className="px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-teal-500 focus:border-transparent"
												disabled
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							{/* Submit Button */}
							<div className="flex justify-end pt-4">
								<Button
									type="submit"
									disabled={isSubmitting}
									className="bg-teal-600 hover:bg-teal-700 text-white px-6 py-2 rounded-xl font-medium transition-colors"
								>
									{isSubmitting
										? t("updating") || "Updating..."
										: t("updateProfile") ||
											"Update Profile"}
								</Button>
							</div>
						</form>
					</Form>
				</div>
			</div>
		</div>
	);
}
