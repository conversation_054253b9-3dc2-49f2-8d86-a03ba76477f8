import Image from "next/image";
import { Heart, Loader2 } from "lucide-react";
import { useTranslations, useLocale } from "next-intl";
import { Link } from "@/lib/i18n/navigation";
import { cn } from "@/lib/utils";
import { EmptyState } from "./empty-state";
import { LoadingState } from "./loading-state";

interface Cat {
	id: string;
	name: string;
	age: string;
	breed: string;
	gender: "male" | "female";
	location: string;
	imageUrl?: string;
	adopted: boolean | null;
	status: string;
}

interface FavoritesGridProps {
	favorites: Cat[] | undefined;
	isLoading: boolean;
	className?: string;
	onToggleFavorite?: (catId: string) => void;
	pendingFavoriteId?: string | null;
}

export function FavoritesGrid({
	favorites,
	isLoading,
	className,
	onToggleFavorite,
	pendingFavoriteId,
}: FavoritesGridProps) {
	const t = useTranslations("profile");
	const catsT = useTranslations("cats");
	const locale = useLocale();
	const isRTL = locale === "ar";

	if (isLoading) {
		return <LoadingState size="lg" />;
	}

	if (!favorites || favorites.length === 0) {
		return (
			<EmptyState
				icon={Heart}
				title={t("noFavoritesYet") || "No favorites yet"}
				description={
					t("startAddingFavorites") ||
					"Start adding cats to your favorites."
				}
				actionButton={
					<Link
						href="/cats"
						className={cn(
							"inline-flex items-center px-6 py-3 bg-teal-500 hover:bg-teal-600 text-white rounded-full font-medium transition-colors min-h-[44px]",
							isRTL ? "space-x-reverse space-x-2" : "space-x-2"
						)}
					>
						<Heart className="w-5 h-5" />
						<span>{catsT("viewAll")}</span>
					</Link>
				}
			/>
		);
	}

	return (
		<div
			className={cn(
				"grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-6",
				className
			)}
		>
			{favorites.map((cat) => (
				<div
					key={cat.id}
					className="bg-white border border-gray-200 rounded-2xl overflow-hidden hover:shadow-lg transition-shadow group"
				>
					{/* Cat Image */}
					<div className="relative h-48 bg-gray-100">
						<Image
							src={cat.imageUrl || "/cat.jpeg"}
							alt={cat.name}
							fill
							className="object-cover group-hover:scale-105 transition-transform duration-300"
							onError={(e) => {
								e.currentTarget.src = "/cat.jpeg";
							}}
						/>
						{/* Favorite Heart */}
						<button
							className={cn(
								"absolute top-3 right-3 w-10 h-10 min-h-[44px] min-w-[44px] bg-white/90 hover:bg-white rounded-full flex items-center justify-center shadow-sm transition-all touch-manipulation cursor-pointer",
								pendingFavoriteId === cat.id &&
									"pointer-events-none opacity-75"
							)}
							onClick={(e) => {
								e.preventDefault();
								e.stopPropagation();
								onToggleFavorite?.(cat.id);
							}}
							disabled={pendingFavoriteId === cat.id}
							aria-label={`${t("removeFromFavorites")} ${cat.name}`}
						>
							{pendingFavoriteId === cat.id ? (
								<Loader2 className="w-5 h-5 animate-spin text-gray-500" />
							) : (
								<Heart
									className={cn(
										"w-5 h-5 transition-colors",
										"text-red-500 fill-red-500 hover:text-red-600 hover:fill-red-600"
									)}
								/>
							)}
						</button>
						{/* Status Badge */}
						<div className="absolute bottom-3 left-3">
							<span
								className={cn(
									"px-3 py-1 rounded-full text-sm font-medium",
									cat.adopted
										? "bg-blue-100 text-blue-800"
										: cat.status === "available"
											? "bg-green-100 text-green-800"
											: "bg-yellow-100 text-yellow-800"
								)}
							>
								{cat.adopted
									? catsT("status.adopted")
									: catsT(`status.${cat.status}`)}
							</span>
						</div>
					</div>

					{/* Cat Info */}
					<div className="p-6">
						<h3 className="text-lg font-display font-semibold text-gray-900 mb-2">
							{cat.name}
						</h3>
						<p className="text-gray-600 mb-4">
							{cat.age} • {cat.breed} •{" "}
							{cat.gender === "male"
								? catsT("filters.male")
								: catsT("filters.female")}
						</p>
						<p className="text-gray-700 mb-4">{cat.location}</p>

						{/* Action Button */}
						<Link
							href={`/cats/${cat.id}`}
							className="w-full inline-flex items-center justify-center px-4 py-3 bg-teal-500 hover:bg-teal-600 text-white rounded-xl font-medium transition-colors min-h-[44px]"
						>
							{catsT("card.viewDetails")}
						</Link>
					</div>
				</div>
			))}
		</div>
	);
}
