"use client";

import type React from "react";
import { useTranslations } from "next-intl";
import { cn } from "@/lib/utils";
import { But<PERSON> } from "@/components/ui/button";
import { Link } from "@/lib/i18n/navigation";
import { Menu, PlusCircle } from "lucide-react";
import type { User, SidebarItem } from "@/lib/types/profile";

// Alias for backward compatibility
type NavigationItem = SidebarItem;

interface DashboardHeaderProps {
	navigationItems: NavigationItem[];
	activeTab: string;
	onMenuClick: () => void;
	isRTL: boolean;
	user: User;
}

export function DashboardHeader({
	navigationItems,
	activeTab,
	onMenuClick,
	isRTL,
	user,
}: DashboardHeaderProps) {
	const t = useTranslations("profile");

	const activeItem = navigationItems.find((item) => item.id === activeTab);

	return (
		<header className="bg-white border-b border-gray-200 p-5.5">
			<div className="flex items-center justify-between">
				<div
					className={cn(
						"flex items-center",
						isRTL ? "space-x-reverse space-x-4" : "space-x-4"
					)}
				>
					{/* Hamburger Menu Button - Mobile Only */}
					<Button
						variant="ghost"
						size="sm"
						onClick={onMenuClick}
						className="lg:hidden min-h-[44px] min-w-[44px] p-2"
					>
						<Menu className="w-5 h-5" />
					</Button>

					<div>
						<h1 className="text-2xl font-display font-bold text-gray-900">
							{t(activeItem?.labelKey || "title")}
						</h1>
						<p className="text-sm text-gray-600">
							{t(
								activeItem?.descriptionKey ||
									"overviewDescription"
							)}
						</p>
					</div>
				</div>
				<div
					className={cn(
						"flex items-center",
						isRTL ? "space-x-reverse space-x-3" : "space-x-3"
					)}
				>
					{/* Create New Cat Button - Only for rescuers and clinics */}
					{(user.role === "rescuer" || user.role === "clinic") && (
						<Button
							variant="outline"
							size="sm"
							className={cn(
								"hidden sm:flex items-center min-h-[44px]",
								isRTL
									? "space-x-reverse space-x-2"
									: "space-x-2"
							)}
							asChild
						>
							<Link href="/cats/new">
								<PlusCircle className="w-4 h-4" />
								<span>{t("addCat")}</span>
							</Link>
						</Button>
					)}
				</div>
			</div>
		</header>
	);
}
