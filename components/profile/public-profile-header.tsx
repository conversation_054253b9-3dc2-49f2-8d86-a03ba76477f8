"use client";

import Image from "next/image";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
	MapPin,
	Calendar,
	MessageCircle,
	UserPlus,
	Star,
	CheckCircle,
} from "lucide-react";
import { useTranslations, useLocale } from "next-intl";
import { useState } from "react";

interface PublicUserProfile {
	id: number;
	name: string;
	slug: string;
	role: "adopter" | "rescuer" | "clinic" | "admin";
	bio: string | null;
	location: string | null;
	image: string | null;
	createdAt: string | Date;
	stats: {
		catsListed: number;
		adoptions: number;
		reviews: number;
		rating: number;
	};
}

interface PublicProfileHeaderProps {
	user: PublicUserProfile;
}

export function PublicProfileHeader({ user }: PublicProfileHeaderProps) {
	const t = useTranslations("profile.public");
	const locale = useLocale();
	const [isFollowing, setIsFollowing] = useState(false);

	// Determine text direction based on locale
	const isRTL = locale === "ar";
	const dir = isRTL ? "rtl" : "ltr";

	// Format the creation date to be used as "joined date"
	const joinedDate = new Date(user.createdAt).toLocaleDateString("en-US", {
		year: "numeric",
		month: "long",
	});

	// Determine if user is verified (for now, we'll use a simple logic)
	const isVerified = user.stats.catsListed > 0 || user.role === "clinic";
	const isTopHelper = user.stats.adoptions >= 5; // Mock logic for top helper

	const handleSendMessage = () => {
		// TODO: Implement send message functionality
		console.log("Send message to", user.name);
	};

	const handleFollow = () => {
		// TODO: Implement follow functionality
		setIsFollowing(!isFollowing);
	};

	const getRoleDisplayName = (role: string) => {
		return t(`roles.${role}`);
	};

	const getRoleBadgeVariant = (role: string) => {
		switch (role) {
			case "rescuer":
				return "default";
			case "clinic":
				return "secondary";
			case "adopter":
				return "outline";
			case "admin":
				return "destructive";
			default:
				return "outline";
		}
	};

	return (
		<Card className="mb-8" dir={dir}>
			<CardContent className="p-6 sm:p-8">
				<div className="flex flex-col lg:flex-row gap-6">
					{/* Left side - Avatar and basic info */}
					<div className="flex flex-col sm:flex-row gap-6 items-start lg:items-center">
						<div className="relative h-24 w-24 sm:h-32 sm:w-32 shrink-0">
							<div className="relative h-full w-full rounded-full overflow-hidden bg-muted">
								<Image
									src={
										user.image ||
										"https://placehold.co/400x400/e2e8f0/94a3b8?text=User"
									}
									alt={user.name}
									fill
									className="object-cover rounded-full"
								/>
							</div>
							{isVerified && (
								<div
									className={`absolute -bottom-1 h-8 w-8 bg-green-500 rounded-full flex items-center justify-center border-2 border-white ${isRTL ? "-left-1" : "-right-1"}`}
								>
									<CheckCircle className="h-4 w-4 text-white" />
								</div>
							)}
						</div>

						<div className="flex-1 space-y-3">
							<div>
								<div className="flex items-center gap-2 mb-2">
									<h1 className="text-2xl sm:text-3xl font-bold">
										{user.name}
									</h1>
									{isVerified && (
										<Badge
											variant="secondary"
											className="text-green-700 bg-green-100"
										>
											{t("verified")}
										</Badge>
									)}
									{isTopHelper && (
										<Badge
											variant="default"
											className="text-orange-700 bg-orange-100"
										>
											{t("topHelper")}
										</Badge>
									)}
								</div>
								<div
									className={`flex items-center gap-2 text-muted-foreground mb-2`}
								>
									<MapPin className="h-4 w-4" />
									<span>
										{user.location ||
											t("locationNotSpecified")}
									</span>
								</div>
								<Badge variant={getRoleBadgeVariant(user.role)}>
									{getRoleDisplayName(user.role)}
								</Badge>
							</div>
						</div>
					</div>

					{/* Right side - Action buttons */}
					<div
						className={`flex flex-col sm:flex-row gap-3 ${isRTL ? "lg:mr-auto" : "lg:ml-auto"}`}
					>
						<Button
							onClick={handleSendMessage}
							className="min-w-[140px] min-h-[44px]"
						>
							<MessageCircle
								className={`h-4 w-4 ${isRTL ? "ml-2" : "mr-2"}`}
							/>
							{t("sendMessage")}
						</Button>
						<Button
							variant="outline"
							onClick={handleFollow}
							className="min-w-[140px] min-h-[44px]"
						>
							<UserPlus
								className={`h-4 w-4 ${isRTL ? "ml-2" : "mr-2"}`}
							/>
							{isFollowing ? t("following") : t("follow")}
						</Button>
					</div>
				</div>

				{/* Statistics section */}
				<div className="mt-6 pt-6 border-t">
					<div className="grid grid-cols-2 sm:grid-cols-4 gap-4 text-center">
						<div>
							<div className="text-2xl font-bold text-primary">
								{user.stats.catsListed}
							</div>
							<div className="text-sm text-muted-foreground">
								{t("stats.catsListed")}
							</div>
						</div>
						<div>
							<div className="text-2xl font-bold text-primary">
								{user.stats.adoptions}
							</div>
							<div className="text-sm text-muted-foreground">
								{t("stats.adoptions")}
							</div>
						</div>
						<div>
							<div className="flex items-center justify-center gap-1">
								<div className="text-2xl font-bold text-primary">
									{user.stats.rating.toFixed(1)}
								</div>
								<Star className="h-5 w-5 text-amber-500 fill-amber-500" />
							</div>
							<div className="text-sm text-muted-foreground">
								{t("stats.rating")}
							</div>
						</div>
						<div>
							<div className="text-2xl font-bold text-primary">
								{user.stats.reviews}
							</div>
							<div className="text-sm text-muted-foreground">
								{t("stats.reviews")}
							</div>
						</div>
					</div>
				</div>

				{/* Member since */}
				<div
					className={`mt-4 pt-4 border-t flex items-center justify-center text-sm text-muted-foreground`}
				>
					<Calendar
						className={`h-4 w-4 ${isRTL ? "ml-2" : "mr-2"}`}
					/>
					<span>
						{t("memberSince")} {joinedDate}
					</span>
				</div>
			</CardContent>
		</Card>
	);
}
