"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { useTranslations, useLocale } from "next-intl";
import { PublicUserCats } from "./public-user-cats";
import { PublicUserAbout } from "./public-user-about";

interface PublicUserProfile {
	id: number;
	name: string;
	slug: string;
	role: "adopter" | "rescuer" | "clinic" | "admin";
	bio: string | null;
	location: string | null;
	image: string | null;
	createdAt: string | Date;
	stats: {
		catsListed: number;
		adoptions: number;
		reviews: number;
		rating: number;
	};
}

interface PublicProfileTabsProps {
	user: PublicUserProfile;
}

export function PublicProfileTabs({ user }: PublicProfileTabsProps) {
	const t = useTranslations("profile.public");
	const locale = useLocale();
	const [activeTab, setActiveTab] = useState("listedCats");

	// Determine text direction based on locale
	const isRTL = locale === "ar";
	const dir = isRTL ? "rtl" : "ltr";

	return (
		<Tabs
			value={activeTab}
			onValueChange={setActiveTab}
			className="w-full"
			dir={dir}
		>
			<TabsList
				className={`grid w-full grid-cols-2 mb-6 ${isRTL ? "flex-row-reverse" : ""}`}
			>
				<TabsTrigger
					value="listedCats"
					className={`flex items-center gap-2 min-h-[44px] px-4 ${isRTL ? "flex-row-reverse" : ""}`}
				>
					<span>{t("tabs.listedCats")}</span>
					{user.stats.catsListed > 0 && (
						<Badge
							variant="secondary"
							className={isRTL ? "mr-1" : "ml-1"}
						>
							{user.stats.catsListed}
						</Badge>
					)}
				</TabsTrigger>
				<TabsTrigger
					value="about"
					className="flex items-center gap-2 min-h-[44px] px-4"
				>
					<span>{t("tabs.about")}</span>
				</TabsTrigger>
			</TabsList>

			<TabsContent value="listedCats" className="mt-0">
				<div className="space-y-6">
					<div
						className={`flex items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}
					>
						<h2 className="text-xl font-semibold">
							{t("listedCats.title")}
						</h2>
						<div className="text-sm text-muted-foreground">
							{t("listedCats.count", {
								count: user.stats.catsListed,
							})}
						</div>
					</div>
					<PublicUserCats userSlug={user.slug} />
				</div>
			</TabsContent>

			<TabsContent value="about" className="mt-0">
				<div className="space-y-6">
					<h2 className="text-xl font-semibold">
						{t("about.title")}
					</h2>
					<PublicUserAbout user={user} />
				</div>
			</TabsContent>
		</Tabs>
	);
}
