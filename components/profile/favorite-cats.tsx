"use client";

import { api } from "@/lib/trpc/react";
import { CatCard } from "@/components/cat-card";
import { Skeleton } from "@/components/ui/skeleton";
import { Button } from "@/components/ui/button";
import { useTranslations } from "next-intl";
import { Link } from "@/lib/i18n/navigation";
import { useFavorites } from "@/hooks/use-favorites";

export function FavoriteCats() {
	const t = useTranslations("profile");
	const catsT = useTranslations("cats");

	// Use favorites hook
	const {
		userFavorites: favoriteCats,
		isLoadingFavorites: isLoading,
		handleToggleFavorite,
		pendingFavoriteId,
	} = useFavorites();

	// For error handling, we'll use the tRPC query directly for this component
	const { error } = api.favorites.getUserFavorites.useQuery();

	if (isLoading) {
		return (
			<div className="space-y-4">
				<Skeleton className="h-8 w-48" />
				<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
					{[...Array(4)].map((_, i) => (
						<Skeleton key={i} className="h-[400px] w-full" />
					))}
				</div>
			</div>
		);
	}

	if (error) {
		return (
			<div className="text-center py-8">
				<h3 className="text-lg font-medium text-destructive">
					{t("errorLoadingFavorites")}
				</h3>
				<p className="mt-1 text-sm text-muted-foreground">
					{error.message || catsT("errors.genericError")}
				</p>
				<Button asChild className="mt-4">
					<Link href="/cats">{catsT("title")}</Link>
				</Button>
			</div>
		);
	}

	if (!favoriteCats?.length) {
		return (
			<div className="text-center py-8">
				<h3 className="text-lg font-medium text-gray-900">
					{t("noFavoriteCats")}
				</h3>
				<p className="mt-1 text-sm text-gray-500">
					{t("startAddingFavorites")}
				</p>
				<Button asChild className="mt-4">
					<Link href="/cats">{catsT("title")}</Link>
				</Button>
			</div>
		);
	}

	return (
		<div className="space-y-4">
			<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
				{favoriteCats.map((cat) => (
					<CatCard
						key={cat.id}
						cat={{
							...cat,
							id: cat.id.toString(),
							imageUrl: cat.imageUrl || "/cat.jpeg",
							isFavorite: true,
							breedId: cat.breedId || 0,
							wilayaId: cat.wilayaId || 0,
							communeId: cat.communeId || 0,
							isDraft: cat.isDraft ?? false,
						}}
						actions={{
							mode: "view",
							onToggleFavorite: handleToggleFavorite,
							togglePending:
								pendingFavoriteId === cat.id.toString(),
						}}
					/>
				))}
			</div>
		</div>
	);
}
