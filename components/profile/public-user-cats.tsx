"use client";

import { <PERSON> } from "@/lib/i18n/navigation";
import Image from "next/image";
import { Card, CardContent, CardFooter } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Heart, MessageCircle, MapPin } from "lucide-react";
import { api } from "@/lib/trpc/react";
import { useTranslations, useLocale } from "next-intl";
import type { CatSummary } from "@/lib/types/cat";
import { Pagination } from "@/components/pagination";
import { useQueryState } from "nuqs";
import { pageParser } from "@/lib/search-params";

interface PublicUserCatsProps {
	userSlug: string;
	adopted?: boolean;
	limit?: number;
}

export function PublicUserCats({
	userSlug,
	adopted,
	limit = 6,
}: PublicUserCatsProps) {
	const t = useTranslations("profile.public.listedCats");
	const catsT = useTranslations("cats");
	const locale = useLocale();

	// Use nuqs for page state management
	const [page] = useQueryState("page", pageParser);

	// Determine text direction based on locale
	const isRTL = locale === "ar";
	const dir = isRTL ? "rtl" : "ltr";

	// Fetch user cats using tRPC
	const { data, isLoading, error } = api.users.getPublicUserCats.useQuery({
		userSlug,
		adopted,
		page,
		limit,
	});

	const cats = (data?.cats || []) as CatSummary[];
	const pagination = data?.pagination;

	if (error) {
		return (
			<div className="text-center py-8" dir={dir}>
				<p className="text-muted-foreground">
					{catsT("error.failedToLoad") || "Error loading cats"}
				</p>
			</div>
		);
	}

	if (isLoading) {
		return (
			<div
				className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
				dir={dir}
			>
				{[1, 2, 3].map((i) => (
					<Card key={i} className="overflow-hidden h-full">
						<div className="h-48 w-full bg-muted animate-pulse" />
						<CardContent className="p-4">
							<div className="h-6 w-3/4 bg-muted animate-pulse mb-2" />
							<div className="h-4 w-1/2 bg-muted animate-pulse mb-3" />
							<div className="h-4 w-full bg-muted animate-pulse mb-2" />
							<div className="h-4 w-3/4 bg-muted animate-pulse" />
						</CardContent>
					</Card>
				))}
			</div>
		);
	}

	if (cats.length === 0) {
		return (
			<div className="text-center py-12" dir={dir}>
				<h3 className="text-xl font-medium mb-2">
					{t("noListedCats")}
				</h3>
				<p className="text-muted-foreground">
					{t("noListedCatsDescription")}
				</p>
			</div>
		);
	}

	return (
		<div>
			<div
				className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
				dir={dir}
			>
				{cats.map((cat) => (
					<Card key={cat.id} className="overflow-hidden h-full">
						<div className="relative h-48 w-full bg-muted">
							<Image
								src={
									cat.imageUrl ||
									"https://placehold.co/400x300/e2e8f0/94a3b8?text=Cat+Photo"
								}
								alt={cat.name}
								fill
								className="object-cover"
								onError={(e) => {
									e.currentTarget.src =
										"https://placehold.co/400x300/e2e8f0/94a3b8?text=Cat+Photo";
								}}
							/>
							{!adopted && (
								<div
									className={`absolute top-2 ${isRTL ? "left-2" : "right-2"}`}
								>
									<Button
										size="icon"
										variant="ghost"
										className="bg-white/80 hover:bg-white/90 rounded-full min-h-[44px] min-w-[44px]"
									>
										<Heart className="h-5 w-5 text-rose-500" />
									</Button>
								</div>
							)}
							{cat.adopted && (
								<div
									className={`absolute top-2 ${isRTL ? "right-2" : "left-2"}`}
								>
									<Badge
										variant="secondary"
										className="bg-green-100 text-green-700"
									>
										{t("adopted")}
									</Badge>
								</div>
							)}
						</div>

						<CardContent className="p-4">
							<div className="flex justify-between items-start mb-2">
								<h3 className="text-xl font-bold">
									{cat.name}
								</h3>
								<div className="flex items-center gap-2">
									<Badge variant="outline">
										{cat.gender}
									</Badge>
								</div>
							</div>

							<div className="flex flex-wrap gap-2 mb-3">
								<Badge variant="outline">{cat.age}</Badge>
								<Badge variant="outline">
									{cat.breed || catsT("breedMixed")}
								</Badge>
								{cat.vaccinated && (
									<Badge
										variant="outline"
										className="text-blue-700 bg-blue-100"
									>
										{catsT("filters.vaccinated")}
									</Badge>
								)}
								{cat.neutered && (
									<Badge
										variant="outline"
										className="text-purple-700 bg-purple-100"
									>
										{catsT("filters.neutered")}
									</Badge>
								)}
							</div>

							<div
								className={`flex items-center text-sm text-muted-foreground ${isRTL ? "flex-row-reverse" : ""}`}
							>
								<MapPin
									className={`h-4 w-4 ${isRTL ? "ml-1" : "mr-1"}`}
								/>
								<span>
									{cat.location ||
										catsT("details.noLocation")}
								</span>
							</div>
						</CardContent>

						<CardFooter className="p-4 pt-0">
							<div className="flex gap-2 w-full">
								<Button asChild className="flex-1 min-h-[44px]">
									<Link href={`/cats/${cat.id}`}>
										{catsT("card.viewDetails")}
									</Link>
								</Button>
								{!adopted && (
									<Button
										variant="outline"
										size="icon"
										className="min-h-[44px] min-w-[44px]"
									>
										<MessageCircle className="h-4 w-4" />
									</Button>
								)}
							</div>
						</CardFooter>
					</Card>
				))}
			</div>

			{/* Pagination */}
			{pagination && pagination.totalPages > 1 && (
				<Pagination
					currentPage={pagination.page}
					totalPages={pagination.totalPages}
				/>
			)}
		</div>
	);
}
