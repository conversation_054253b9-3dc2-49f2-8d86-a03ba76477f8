"use client";

import { useState } from "react";
import { useTranslations } from "next-intl";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from "@/components/ui/popover";
import { Checkbox } from "@/components/ui/checkbox";
import { Plus, X, Search } from "lucide-react";
import { SERVICE_CATEGORIES, COMMON_SERVICES } from "@/lib/types/clinic";

interface ServicesSelectorProps {
	selectedServices: string[];
	onServicesChange: (services: string[]) => void;
	disabled?: boolean;
}

export function ServicesSelector({
	selectedServices,
	onServicesChange,
	disabled = false,
}: ServicesSelectorProps) {
	const [isOpen, setIsOpen] = useState(false);
	const [searchQuery, setSearchQuery] = useState("");
	const [customService, setCustomService] = useState("");
	const t = useTranslations("profile.clinic");

	// Handle service toggle
	const handleServiceToggle = (service: string, checked: boolean) => {
		if (checked) {
			onServicesChange([...selectedServices, service]);
		} else {
			onServicesChange(selectedServices.filter((s) => s !== service));
		}
	};

	// Handle custom service addition
	const handleAddCustomService = () => {
		if (
			customService.trim() &&
			!selectedServices.includes(customService.trim())
		) {
			onServicesChange([...selectedServices, customService.trim()]);
			setCustomService("");
		}
	};

	// Handle service removal
	const handleRemoveService = (service: string) => {
		onServicesChange(selectedServices.filter((s) => s !== service));
	};

	return (
		<div className="space-y-3">
			{/* Selected Services Display */}
			<div className="flex flex-wrap gap-2">
				{selectedServices.map((service) => (
					<Badge
						key={service}
						variant="secondary"
						className="bg-teal-50 text-teal-700 border-teal-200 px-3 py-1 flex items-center gap-2"
					>
						{service}
						{!disabled && (
							<button
								type="button"
								onClick={() => handleRemoveService(service)}
								className="hover:bg-teal-100 rounded-full p-0.5"
							>
								<X className="w-3 h-3" />
							</button>
						)}
					</Badge>
				))}
			</div>

			{/* Add Services Button */}
			{!disabled && (
				<Popover open={isOpen} onOpenChange={setIsOpen}>
					<PopoverTrigger asChild>
						<Button
							type="button"
							variant="outline"
							className="border-dashed border-2 border-gray-300 hover:border-teal-500 hover:bg-teal-50 text-gray-600 hover:text-teal-700"
						>
							<Plus className="w-4 h-4 mr-2" />
							{t("addServices") || "Add Services"}
						</Button>
					</PopoverTrigger>
					<PopoverContent className="w-80 p-4" align="start">
						<div className="space-y-4">
							{/* Search */}
							<div className="relative">
								<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
								<Input
									placeholder={
										t("searchServices") ||
										"Search services..."
									}
									value={searchQuery}
									onChange={(e) =>
										setSearchQuery(e.target.value)
									}
									className="pl-10"
								/>
							</div>

							{/* Common Services */}
							<div className="max-h-60 overflow-y-auto">
								<h4 className="font-medium text-sm text-gray-700 mb-3">
									{t("commonServices") || "Common Services"}
								</h4>
								<div className="space-y-2">
									{COMMON_SERVICES.filter((service) =>
										service
											.toLowerCase()
											.includes(searchQuery.toLowerCase())
									).map((service) => (
										<div
											key={service}
											className="flex items-center space-x-2"
										>
											<Checkbox
												id={service}
												checked={selectedServices.includes(
													service
												)}
												onCheckedChange={(checked) =>
													handleServiceToggle(
														service,
														checked as boolean
													)
												}
											/>
											<label
												htmlFor={service}
												className="text-sm text-gray-700 cursor-pointer flex-1"
											>
												{service}
											</label>
										</div>
									))}
								</div>
							</div>

							{/* Custom Service Input */}
							<div className="border-t pt-3">
								<div className="flex gap-2">
									<Input
										placeholder={
											t("customService") ||
											"Add custom service..."
										}
										value={customService}
										onChange={(e) =>
											setCustomService(e.target.value)
										}
										onKeyPress={(e) => {
											if (e.key === "Enter") {
												e.preventDefault();
												handleAddCustomService();
											}
										}}
										className="flex-1"
									/>
									<Button
										type="button"
										size="sm"
										onClick={handleAddCustomService}
										disabled={!customService.trim()}
									>
										<Plus className="w-4 h-4" />
									</Button>
								</div>
							</div>
						</div>
					</PopoverContent>
				</Popover>
			)}

			{selectedServices.length === 0 && (
				<p className="text-sm text-gray-500">
					{t("noServicesSelected") ||
						"No services selected. Add services to help clients find your clinic."}
				</p>
			)}
		</div>
	);
}
