"use client";

import { useState } from "react";
import { useTranslations } from "next-intl";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Clock, Plus, Trash2 } from "lucide-react";
import type { OperatingHours, DaySchedule } from "@/lib/types/clinic";

interface OperatingHoursEditorProps {
	operatingHours?: OperatingHours | null;
	onOperatingHoursChange: (hours: OperatingHours) => void;
	disabled?: boolean;
}

const DAYS_OF_WEEK = [
	"monday",
	"tuesday", 
	"wednesday",
	"thursday",
	"friday",
	"saturday",
	"sunday",
] as const;

export function OperatingHoursEditor({
	operatingHours,
	onOperatingHoursChange,
	disabled = false,
}: OperatingHoursEditorProps) {
	const t = useTranslations("profile.clinic");
	const commonT = useTranslations("common");

	// Initialize default operating hours if none provided
	const defaultHours: OperatingHours = {
		monday: { isOpen: true, periods: [{ open: "09:00", close: "17:00" }] },
		tuesday: { isOpen: true, periods: [{ open: "09:00", close: "17:00" }] },
		wednesday: { isOpen: true, periods: [{ open: "09:00", close: "17:00" }] },
		thursday: { isOpen: true, periods: [{ open: "09:00", close: "17:00" }] },
		friday: { isOpen: true, periods: [{ open: "09:00", close: "17:00" }] },
		saturday: { isOpen: true, periods: [{ open: "09:00", close: "13:00" }] },
		sunday: { isOpen: false, periods: [] },
	};

	const currentHours = operatingHours || defaultHours;

	// Handle day open/closed toggle
	const handleDayToggle = (day: keyof OperatingHours, isOpen: boolean) => {
		const updatedHours = {
			...currentHours,
			[day]: {
				...currentHours[day],
				isOpen,
				periods: isOpen ? (currentHours[day].periods.length > 0 ? currentHours[day].periods : [{ open: "09:00", close: "17:00" }]) : [],
			},
		};
		onOperatingHoursChange(updatedHours);
	};

	// Handle time period change
	const handlePeriodChange = (
		day: keyof OperatingHours,
		periodIndex: number,
		field: "open" | "close",
		value: string
	) => {
		const updatedHours = { ...currentHours };
		const daySchedule = { ...updatedHours[day] };
		const periods = [...daySchedule.periods];
		periods[periodIndex] = { ...periods[periodIndex], [field]: value };
		daySchedule.periods = periods;
		updatedHours[day] = daySchedule;
		onOperatingHoursChange(updatedHours);
	};

	// Add new time period for a day
	const handleAddPeriod = (day: keyof OperatingHours) => {
		const updatedHours = { ...currentHours };
		const daySchedule = { ...updatedHours[day] };
		daySchedule.periods = [...daySchedule.periods, { open: "09:00", close: "17:00" }];
		updatedHours[day] = daySchedule;
		onOperatingHoursChange(updatedHours);
	};

	// Remove time period
	const handleRemovePeriod = (day: keyof OperatingHours, periodIndex: number) => {
		const updatedHours = { ...currentHours };
		const daySchedule = { ...updatedHours[day] };
		daySchedule.periods = daySchedule.periods.filter((_, index) => index !== periodIndex);
		updatedHours[day] = daySchedule;
		onOperatingHoursChange(updatedHours);
	};

	// Get day name translation
	const getDayName = (day: string) => {
		return commonT(`days.${day}`) || day.charAt(0).toUpperCase() + day.slice(1);
	};

	return (
		<Card>
			<CardHeader>
				<CardTitle className="flex items-center text-lg">
					<Clock className="w-5 h-5 mr-2 text-teal-600" />
					{t("operatingHours") || "Operating Hours"}
				</CardTitle>
			</CardHeader>
			<CardContent className="space-y-4">
				{DAYS_OF_WEEK.map((day) => {
					const daySchedule = currentHours[day];
					return (
						<div key={day} className="border border-gray-200 rounded-lg p-4">
							<div className="flex items-center justify-between mb-3">
								<div className="flex items-center space-x-3">
									<Checkbox
										id={`${day}-open`}
										checked={daySchedule.isOpen}
										onCheckedChange={(checked) =>
											handleDayToggle(day, checked as boolean)
										}
										disabled={disabled}
									/>
									<label
										htmlFor={`${day}-open`}
										className="font-medium text-gray-900 cursor-pointer"
									>
										{getDayName(day)}
									</label>
								</div>
								{daySchedule.isOpen && !disabled && (
									<Button
										type="button"
										variant="outline"
										size="sm"
										onClick={() => handleAddPeriod(day)}
										className="text-teal-600 border-teal-300 hover:bg-teal-50"
									>
										<Plus className="w-4 h-4 mr-1" />
										{t("addPeriod") || "Add Period"}
									</Button>
								)}
							</div>

							{daySchedule.isOpen && (
								<div className="space-y-2">
									{daySchedule.periods.map((period, periodIndex) => (
										<div
											key={periodIndex}
											className="flex items-center space-x-3 bg-gray-50 p-3 rounded-md"
										>
											<div className="flex items-center space-x-2 flex-1">
												<Input
													type="time"
													value={period.open}
													onChange={(e) =>
														handlePeriodChange(day, periodIndex, "open", e.target.value)
													}
													disabled={disabled}
													className="w-32"
												/>
												<span className="text-gray-500">
													{t("to") || "to"}
												</span>
												<Input
													type="time"
													value={period.close}
													onChange={(e) =>
														handlePeriodChange(day, periodIndex, "close", e.target.value)
													}
													disabled={disabled}
													className="w-32"
												/>
											</div>
											{daySchedule.periods.length > 1 && !disabled && (
												<Button
													type="button"
													variant="outline"
													size="sm"
													onClick={() => handleRemovePeriod(day, periodIndex)}
													className="text-red-600 border-red-300 hover:bg-red-50"
												>
													<Trash2 className="w-4 h-4" />
												</Button>
											)}
										</div>
									))}
									{daySchedule.periods.length === 0 && daySchedule.isOpen && (
										<p className="text-sm text-gray-500 italic">
											{t("noPeriods") || "No time periods set for this day"}
										</p>
									)}
								</div>
							)}

							{!daySchedule.isOpen && (
								<p className="text-sm text-gray-500 italic ml-6">
									{t("closed") || "Closed"}
								</p>
							)}
						</div>
					);
				})}

				<div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
					<p className="text-sm text-blue-800">
						<strong>{t("note") || "Note"}:</strong>{" "}
						{t("operatingHoursNote") || 
							"These hours will be displayed on your public clinic profile. You can add multiple time periods for days with breaks (e.g., lunch break)."}
					</p>
				</div>
			</CardContent>
		</Card>
	);
}
