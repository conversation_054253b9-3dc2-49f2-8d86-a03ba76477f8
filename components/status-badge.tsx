import { Badge } from "@/components/ui/badge";
import { CheckCircle, Clock, AlertCircle, LockIcon } from "lucide-react";
import { useTranslations } from "next-intl";

interface StatusBadgeProps {
	status: string;
}

export function StatusBadge({ status }: StatusBadgeProps) {
	const t = useTranslations("cats");

	switch (status.toLowerCase()) {
		case "approved":
			return (
				<Badge
					variant="outline"
					className="bg-white border-green-500 text-green-500"
				>
					<CheckCircle className="h-3.5 w-3.5 mr-1" />
					{t("status.approved")}
				</Badge>
			);
		case "pending":
			return (
				<Badge
					variant="outline"
					className="bg-white border-amber-500 text-amber-500"
				>
					<Clock className="h-3.5 w-3.5 mr-1" />
					{t("status.pending")}
				</Badge>
			);
		case "rejected":
			return (
				<Badge
					variant="outline"
					className="bg-white border-red-500 text-red-500"
				>
					<AlertCircle className="h-3.5 w-3.5 mr-1" />
					{t("status.rejected")}
				</Badge>
			);
		case "available":
			return (
				<Badge
					variant="outline"
					className="bg-white border-green-500 text-green-500"
				>
					<CheckCircle className="h-3.5 w-3.5 mr-1" />
					{t("status.available")}
				</Badge>
			);
		case "adopted":
			return (
				<Badge
					variant="outline"
					className="bg-white border-blue-500 text-blue-500"
				>
					<CheckCircle className="h-3.5 w-3.5 mr-1" />
					{t("status.adopted")}
				</Badge>
			);
		case "unavailable":
			return (
				<Badge
					variant="outline"
					className="bg-white border-red-500 text-red-500"
				>
					<LockIcon className="h-3.5 w-3.5 mr-1" />
					{t("status.unavailable")}
				</Badge>
			);
		default:
			return <Badge variant="outline">{status}</Badge>;
	}
}
