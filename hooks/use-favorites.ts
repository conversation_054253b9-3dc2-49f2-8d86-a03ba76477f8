"use client";

import { useState } from "react";
import { api } from "@/lib/trpc/react";
import { useToast } from "@/hooks/use-toast";
import { useTranslations } from "next-intl";
import { TRPCClientError } from "@trpc/client";
import { useSession } from "@/lib/auth/client";

interface UseFavoritesOptions {
	/**
	 * Additional queries to invalidate when favorites change
	 */
	additionalQueriesToInvalidate?: Array<() => void>;
	/**
	 * Custom success message for adding to favorites
	 */
	onAddSuccess?: (catName?: string) => void;
	/**
	 * Custom success message for removing from favorites
	 */
	onRemoveSuccess?: (catName?: string) => void;
	/**
	 * Whether to show toast notifications
	 */
	showToasts?: boolean;
}

export function useFavorites(options: UseFavoritesOptions = {}) {
	const {
		additionalQueriesToInvalidate = [],
		onAddSuccess,
		onRemoveSuccess,
		showToasts = true,
	} = options;

	const { data: session } = useSession();

	const utils = api.useUtils();
	const { toast } = useToast();
	const t = useTranslations("profile");
	const [pendingFavoriteId, setPendingFavoriteId] = useState<string | null>(
		null
	);

	// Fetch user's favorites if logged in
	const { data: userFavorites, isLoading: isLoadingFavorites } =
		api.favorites.getUserFavorites.useQuery(undefined, {
			enabled: !!session?.user,
		});

	// Create a map of favorite status for quick lookup
	const favoriteMap = new Map(
		userFavorites?.map((cat) => [cat.id, true]) || []
	);

	// Toggle favorite mutation
	const toggleFavorite = api.favorites.toggle.useMutation({
		onSuccess: (result, variables) => {
			// Invalidate favorites queries
			utils.favorites.getUserFavorites.invalidate();

			// Invalidate additional queries if provided
			additionalQueriesToInvalidate.forEach((invalidate) => {
				try {
					invalidate();
				} catch (error) {
					console.warn(
						"Failed to invalidate additional query:",
						error
					);
				}
			});

			setPendingFavoriteId(null);

			// Show success toast if enabled
			if (showToasts) {
				if (result.isFavorite) {
					if (onAddSuccess) {
						onAddSuccess();
					} else {
						toast({
							title: t("favoriteAdded") || "Added to favorites",
							description:
								t("favoriteAddedDescription") ||
								"Cat has been added to your favorites.",
						});
					}
				} else {
					if (onRemoveSuccess) {
						onRemoveSuccess();
					} else {
						toast({
							title:
								t("favoriteRemoved") ||
								"Removed from favorites",
							description:
								t("favoriteRemovedDescription") ||
								"Cat has been removed from your favorites.",
						});
					}
				}
			}
		},
		onError: (error) => {
			setPendingFavoriteId(null);

			// Handle different types of errors
			let errorMessage = "Something went wrong";

			if (error instanceof TRPCClientError) {
				switch (error.data?.code) {
					case "UNAUTHORIZED":
						errorMessage = "Please log in to manage favorites";
						break;
					case "NOT_FOUND":
						errorMessage = "Cat not found";
						break;
					case "INTERNAL_SERVER_ERROR":
						errorMessage = "Server error. Please try again.";
						break;
					default:
						errorMessage =
							error.message || "Failed to update favorites";
				}
			}

			// Show error toast if enabled
			if (showToasts) {
				toast({
					variant: "destructive",
					title: "Error",
					description: errorMessage,
				});
			}

			console.error("Failed to toggle favorite:", error);
		},
	});

	// Handle toggling favorite status
	const handleToggleFavorite = async (catId: string, catName?: string) => {
		setPendingFavoriteId(catId);
		try {
			await toggleFavorite.mutateAsync({
				catId: parseInt(catId),
			});
		} catch (error) {
			// Error handling is done in the mutation's onError callback
			console.error("Failed to toggle favorite:", error);
		}
	};

	// Check if a cat is favorited
	const isFavorite = (catId: string | number) => {
		const id = typeof catId === "string" ? catId : catId.toString();
		return favoriteMap.has(id);
	};

	// Check if a specific cat is pending
	const isPending = (catId: string) => {
		return pendingFavoriteId === catId;
	};

	return {
		// Data
		userFavorites,
		favoriteMap,
		isLoadingFavorites,

		// State
		pendingFavoriteId,

		// Actions
		handleToggleFavorite,

		// Utilities
		isFavorite,
		isPending,

		// Raw mutation for advanced usage
		toggleFavorite,
	};
}
