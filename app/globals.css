@import "tailwindcss";

@source '../*.{js,ts,jsx,tsx,mdx}';

@custom-variant dark (&:is(.dark *));

@theme {
	--color-background: hsl(var(--background));
	--color-foreground: hsl(var(--foreground));

	--color-card: hsl(var(--card));
	--color-card-foreground: hsl(var(--card-foreground));

	--color-popover: hsl(var(--popover));
	--color-popover-foreground: hsl(var(--popover-foreground));

	--color-primary: hsl(var(--primary));
	--color-primary-foreground: hsl(var(--primary-foreground));

	--color-secondary: hsl(var(--secondary));
	--color-secondary-foreground: hsl(var(--secondary-foreground));

	--color-muted: hsl(var(--muted));
	--color-muted-foreground: hsl(var(--muted-foreground));

	--color-accent: hsl(var(--accent));
	--color-accent-foreground: hsl(var(--accent-foreground));

	/* Custom colors for redesign */
	--color-teal-50: #f0fdfa;
	--color-teal-500: #14b8a6;
	--color-teal-600: #0d9488;
	--color-teal-700: #0f766e;

	--color-coral-50: #fef7f0;
	--color-coral-400: #fb7185;
	--color-coral-500: #f43f5e;

	/* Custom font families */
	--font-display: "Inter", system-ui, sans-serif;

	--color-destructive: hsl(var(--destructive));
	--color-destructive-foreground: hsl(var(--destructive-foreground));

	--color-border: hsl(var(--border));
	--color-input: hsl(var(--input));
	--color-ring: hsl(var(--ring));

	--color-chart-1: hsl(var(--chart-1));
	--color-chart-2: hsl(var(--chart-2));
	--color-chart-3: hsl(var(--chart-3));
	--color-chart-4: hsl(var(--chart-4));
	--color-chart-5: hsl(var(--chart-5));

	--color-sidebar: hsl(var(--sidebar-background));
	--color-sidebar-foreground: hsl(var(--sidebar-foreground));
	--color-sidebar-primary: hsl(var(--sidebar-primary));
	--color-sidebar-primary-foreground: hsl(var(--sidebar-primary-foreground));
	--color-sidebar-accent: hsl(var(--sidebar-accent));
	--color-sidebar-accent-foreground: hsl(var(--sidebar-accent-foreground));
	--color-sidebar-border: hsl(var(--sidebar-border));
	--color-sidebar-ring: hsl(var(--sidebar-ring));

	--radius-lg: var(--radius);
	--radius-md: calc(var(--radius) - 2px);
	--radius-sm: calc(var(--radius) - 4px);

	--animate-accordion-down: accordion-down 0.2s ease-out;
	--animate-accordion-up: accordion-up 0.2s ease-out;

	@keyframes accordion-down {
		from {
			height: 0;
		}
		to {
			height: var(--radix-accordion-content-height);
		}
	}
	@keyframes accordion-up {
		from {
			height: var(--radix-accordion-content-height);
		}
		to {
			height: 0;
		}
	}
}

/*
  The default border color has changed to `currentcolor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
	*,
	::after,
	::before,
	::backdrop,
	::file-selector-button {
		border-color: var(--color-gray-200, currentcolor);
	}
}

@utility text-balance {
	text-wrap: balance;
}

@layer utilities {
	body {
		font-family: Arial, Helvetica, sans-serif;
	}

	/* Custom utilities for the redesign */
	.font-display {
		font-family: var(--font-display);
		font-weight: 600;
	}

	.card-hover {
		transition: all 0.3s ease;
	}

	.card-hover:hover {
		transform: translateY(-2px);
		box-shadow:
			0 10px 25px -5px rgba(20, 184, 166, 0.1),
			0 8px 10px -6px rgba(20, 184, 166, 0.1);
	}

	/* Enhanced hover effects for cat cards with group hover */
	.card-hover.group:hover {
		transform: translateY(-4px);
		box-shadow:
			0 20px 40px -10px rgba(20, 184, 166, 0.15),
			0 10px 20px -5px rgba(20, 184, 166, 0.08);
	}

	.gradient-hero {
		background: linear-gradient(
			135deg,
			#f0fdfa 0%,
			#ffffff 50%,
			#fef7f0 100%
		);
	}

	.backdrop-blur-nav {
		backdrop-filter: blur(8px);
		background-color: rgba(255, 255, 255, 0.95);
	}

	.warm-shadow {
		box-shadow:
			0 4px 6px -1px hsl(var(--primary) / 0.1),
			0 2px 4px -2px hsl(var(--primary) / 0.1);
	}

	.warm-shadow-lg {
		box-shadow:
			0 10px 15px -3px hsl(var(--primary) / 0.1),
			0 4px 6px -4px hsl(var(--primary) / 0.1);
	}

	/* Responsive utilities */
	.touch-target {
		min-height: 44px;
		min-width: 44px;
	}

	/* Hero title responsive classes */
	.hero-title {
		@apply text-3xl font-display font-bold;
	}

	.cat-grid {
		@apply grid grid-cols-1 gap-4;
	}

	.search-bar {
		@apply flex flex-col space-y-4;
	}

	/* Tablet styles */
	@media (min-width: 768px) {
		.hero-title {
			@apply text-5xl;
		}

		.cat-grid {
			@apply grid-cols-2 gap-6;
		}

		.search-bar {
			@apply flex-row space-y-0 space-x-4;
		}
	}

	/* Desktop styles */
	@media (min-width: 1024px) {
		.hero-title {
			@apply text-6xl;
		}

		.cat-grid {
			@apply grid-cols-3;
		}
	}

	/* Large desktop styles */
	@media (min-width: 1440px) {
		.cat-grid {
			@apply grid-cols-4;
		}
	}

	/* Mobile-first responsive text sizes */
	.text-responsive-sm {
		font-size: 0.875rem;
		line-height: 1.25rem;
	}

	@media (min-width: 768px) {
		.text-responsive-sm {
			font-size: 1rem;
			line-height: 1.5rem;
		}
	}

	.text-responsive-base {
		font-size: 1rem;
		line-height: 1.5rem;
	}

	@media (min-width: 768px) {
		.text-responsive-base {
			font-size: 1.125rem;
			line-height: 1.75rem;
		}
	}

	@media (min-width: 1024px) {
		.text-responsive-base {
			font-size: 1.25rem;
			line-height: 1.75rem;
		}
	}

	/* RTL/LTR support utilities */
	.rtl-flip {
		transform: scaleX(-1);
	}

	[dir="rtl"] .rtl-flip {
		transform: scaleX(1);
	}

	.rtl-space-reverse {
		flex-direction: row-reverse;
	}

	[dir="ltr"] .rtl-space-reverse {
		flex-direction: row;
	}

	/* Ensure proper spacing for RTL */
	[dir="rtl"] .space-x-2 > :not([hidden]) ~ :not([hidden]) {
		margin-right: 0.5rem;
		margin-left: 0;
	}

	[dir="rtl"] .space-x-4 > :not([hidden]) ~ :not([hidden]) {
		margin-right: 1rem;
		margin-left: 0;
	}

	[dir="rtl"] .space-x-6 > :not([hidden]) ~ :not([hidden]) {
		margin-right: 1.5rem;
		margin-left: 0;
	}
}

@layer base {
	:root {
		/* Updated color scheme based on reference design */
		--background: 45 15% 97%;
		--foreground: 220 13% 18%;
		--card: 0 0% 100%;
		--card-foreground: 220 13% 18%;
		--popover: 0 0% 100%;
		--popover-foreground: 220 13% 18%;
		--primary: 15 85% 60%;
		--primary-foreground: 0 0% 98%;
		--secondary: 45 25% 92%;
		--secondary-foreground: 220 13% 18%;
		--muted: 45 15% 94%;
		--muted-foreground: 220 9% 46%;
		--accent: 35 100% 95%;
		--accent-foreground: 220 13% 18%;
		--destructive: 0 84.2% 60.2%;
		--destructive-foreground: 0 0% 98%;
		--border: 45 15% 88%;
		--input: 45 15% 88%;
		--ring: 15 85% 60%;
		--chart-1: 15 85% 60%;
		--chart-2: 35 70% 55%;
		--chart-3: 25 80% 50%;
		--chart-4: 45 75% 65%;
		--chart-5: 55 70% 60%;
		--radius: 0.75rem;
		--sidebar-background: 45 15% 97%;
		--sidebar-foreground: 220 13% 18%;
		--sidebar-primary: 15 85% 60%;
		--sidebar-primary-foreground: 0 0% 98%;
		--sidebar-accent: 45 25% 92%;
		--sidebar-accent-foreground: 220 13% 18%;
		--sidebar-border: 45 15% 88%;
		--sidebar-ring: 15 85% 60%;
	}
	.dark {
		/* Dark theme with warm accent colors */
		--background: 220 13% 9%;
		--foreground: 45 15% 95%;
		--card: 220 13% 12%;
		--card-foreground: 45 15% 95%;
		--popover: 220 13% 12%;
		--popover-foreground: 45 15% 95%;
		--primary: 15 85% 65%;
		--primary-foreground: 220 13% 9%;
		--secondary: 220 13% 16%;
		--secondary-foreground: 45 15% 95%;
		--muted: 220 13% 16%;
		--muted-foreground: 220 9% 65%;
		--accent: 220 13% 16%;
		--accent-foreground: 45 15% 95%;
		--destructive: 0 62.8% 50%;
		--destructive-foreground: 45 15% 95%;
		--border: 220 13% 20%;
		--input: 220 13% 20%;
		--ring: 15 85% 65%;
		--chart-1: 15 85% 65%;
		--chart-2: 35 70% 60%;
		--chart-3: 25 80% 55%;
		--chart-4: 45 75% 70%;
		--chart-5: 55 70% 65%;
		--sidebar-background: 220 13% 9%;
		--sidebar-foreground: 45 15% 95%;
		--sidebar-primary: 15 85% 65%;
		--sidebar-primary-foreground: 220 13% 9%;
		--sidebar-accent: 220 13% 16%;
		--sidebar-accent-foreground: 45 15% 95%;
		--sidebar-border: 220 13% 20%;
		--sidebar-ring: 15 85% 65%;
	}
}

@layer base {
	* {
		@apply border-border;
	}
	body {
		@apply bg-background text-foreground;
	}
}
