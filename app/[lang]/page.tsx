import { <PERSON> } from "@/lib/i18n/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { FeaturedCats } from "@/components/featured-cats";
import { Stats } from "@/components/stats";
import { HeroSection } from "@/components/hero-section";
import { HowItWorks } from "@/components/how-it-works";
import { getTranslations } from "next-intl/server";
import { setRequestLocale } from "next-intl/server";

export default async function Home({
	params,
}: {
	params: Promise<{ lang: string }>;
}) {
	// Await the params before using them
	const { lang } = await params;

	// Enable static rendering
	setRequestLocale(lang);

	// Get translations
	const t = await getTranslations("home");

	return (
		<main className="min-h-screen flex flex-col">
			<HeroSection />

			{/* Mission Section */}
			<section className="w-full py-16 lg:py-20">
				<div className="container max-w-4xl mx-auto px-4">
					<div className="text-center space-y-6 lg:space-y-8">
						<h2 className="text-3xl lg:text-4xl font-bold tracking-tight text-balance">
							{t("mission.title")}
						</h2>
						<div className="space-y-4">
							<p className="text-lg lg:text-xl text-muted-foreground leading-relaxed">
								{t("mission.description")}
							</p>
							<div className="bg-primary/5 border border-primary/20 rounded-2xl p-6 lg:p-8">
								<p className="font-semibold text-primary text-base lg:text-lg">
									{t("mission.disclaimer")}
								</p>
							</div>
						</div>
					</div>
				</div>
			</section>

			{/* Featured Cats Section */}
			<section className="w-full bg-muted/30 py-16 lg:py-20">
				<div className="container max-w-7xl mx-auto px-4">
					<div className="flex flex-col items-center space-y-8 lg:space-y-12">
						<div className="text-center space-y-4">
							<h2 className="text-3xl lg:text-4xl font-bold tracking-tight text-balance">
								{t("featured.title")}
							</h2>
							<p className="text-lg text-muted-foreground max-w-2xl">
								{t("featured.subtitle")}
							</p>
						</div>
						<div className="w-full flex justify-center">
							<FeaturedCats />
						</div>
						<div className="text-center">
							<Button
								asChild
								size="lg"
								className="h-12 px-8 text-base font-semibold rounded-full warm-shadow hover:warm-shadow-lg transition-all duration-300 min-h-[44px]"
							>
								<Link href="/cats">
									{t("featured.viewAll")}
								</Link>
							</Button>
						</div>
					</div>
				</div>
			</section>

			{/* How It Works Section */}
			<HowItWorks />

			{/* Stats Section */}
			<section className="w-full py-16 lg:py-20">
				<div className="container max-w-6xl mx-auto px-4">
					<div className="text-center mb-12 lg:mb-16">
						<h2 className="text-3xl lg:text-4xl font-bold tracking-tight text-balance mb-4">
							{t("stats.title")}
						</h2>
					</div>
					<Stats />
				</div>
			</section>

			{/* Roles Section */}
			<section className="w-full bg-accent/20 py-16 lg:py-20">
				<div className="container max-w-6xl mx-auto px-4">
					<div className="text-center mb-12 lg:mb-16">
						<h2 className="text-3xl lg:text-4xl font-bold tracking-tight text-balance mb-4">
							{t("roles.title")}
						</h2>
						<p className="text-lg text-muted-foreground max-w-2xl mx-auto">
							{t("roles.subtitle")}
						</p>
					</div>
					<div className="grid md:grid-cols-3 gap-6 lg:gap-8">
						<Card className="warm-shadow rounded-2xl border-0 bg-card/80 backdrop-blur-sm card-hover">
							<CardContent className="p-6 lg:p-8 text-center">
								<div className="mb-6">
									<div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
										<span className="text-2xl">🏠</span>
									</div>
									<h3 className="text-xl lg:text-2xl font-bold mb-3">
										{t("roles.adopter.title")}
									</h3>
								</div>
								<p className="text-muted-foreground mb-6 leading-relaxed">
									{t("roles.adopter.description")}
								</p>
								<Button
									asChild
									variant="outline"
									className="w-full h-12 rounded-full border-2 hover:bg-primary/5 transition-all duration-300 min-h-[44px]"
								>
									<Link href="/cats">
										{t("roles.adopter.cta")}
									</Link>
								</Button>
							</CardContent>
						</Card>

						<Card className="warm-shadow rounded-2xl border-0 bg-card/80 backdrop-blur-sm card-hover">
							<CardContent className="p-6 lg:p-8 text-center">
								<div className="mb-6">
									<div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
										<span className="text-2xl">❤️</span>
									</div>
									<h3 className="text-xl lg:text-2xl font-bold mb-3">
										{t("roles.rescuer.title")}
									</h3>
								</div>
								<p className="text-muted-foreground mb-6 leading-relaxed">
									{t("roles.rescuer.description")}
								</p>
								<Button
									asChild
									variant="outline"
									className="w-full h-12 rounded-full border-2 hover:bg-primary/5 transition-all duration-300 min-h-[44px]"
								>
									<Link href="/auth/register">
										{t("roles.rescuer.cta")}
									</Link>
								</Button>
							</CardContent>
						</Card>

						<Card className="warm-shadow rounded-2xl border-0 bg-card/80 backdrop-blur-sm card-hover">
							<CardContent className="p-6 lg:p-8 text-center">
								<div className="mb-6">
									<div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
										<span className="text-2xl">🏥</span>
									</div>
									<h3 className="text-xl lg:text-2xl font-bold mb-3">
										{t("roles.clinic.title")}
									</h3>
								</div>
								<p className="text-muted-foreground mb-6 leading-relaxed">
									{t("roles.clinic.description")}
								</p>
								<Button
									asChild
									variant="outline"
									className="w-full h-12 rounded-full border-2 hover:bg-primary/5 transition-all duration-300 min-h-[44px]"
								>
									<Link href="/auth/register?role=clinic">
										{t("roles.clinic.cta")}
									</Link>
								</Button>
							</CardContent>
						</Card>
					</div>
				</div>
			</section>
		</main>
	);
}
