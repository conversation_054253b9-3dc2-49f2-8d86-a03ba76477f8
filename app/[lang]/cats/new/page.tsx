import { redirect } from "next/navigation";
import { auth } from "@/lib/auth/auth";
import { db } from "@/lib/db";
import { users } from "@/lib/db/schema";
import { eq } from "drizzle-orm";
import { headers } from "next/headers";
import { CatForm } from "@/components/cat-form";
import { getTranslations } from "next-intl/server";
import { setRequestLocale } from "next-intl/server";
import type { Locale } from "@/lib/i18n/routing";

export async function generateMetadata({
	params,
}: {
	params: { lang: Locale };
}) {
	const { lang } = await params;
	const t = await getTranslations({ locale: lang, namespace: "cats.new" });
	const common = await getTranslations({ locale: lang, namespace: "common" });

	return {
		title: `${t("title") || "Add New Cat"} - ${common("appName")}`,
		description: t("description") || "List a new cat for adoption",
	};
}

export default async function AddCatPage({
	params,
}: {
	params: Promise<{ lang: Locale }>;
}) {
	const { lang } = await params;

	// Enable static rendering
	setRequestLocale(lang);

	// Get authenticated user
	const session = await auth.api.getSession({
		headers: await headers(),
	});

	if (!session) {
		redirect(`/${lang}/auth/login?callbackUrl=/${lang}/cats/new`);
	}

	// Get user details from database
	const userId = parseInt(session.user.id);
	const user = await db.query.users.findFirst({
		where: eq(users.id, userId),
	});

	// Redirect if not rescuer or clinic
	if (!user || (user.role !== "rescuer" && user.role !== "clinic")) {
		redirect(`/${lang}`);
	}

	return (
		<main className="container py-10 flex flex-col items-center">
			<div className="w-full max-w-3xl">
				<CatForm />
			</div>
		</main>
	);
}
