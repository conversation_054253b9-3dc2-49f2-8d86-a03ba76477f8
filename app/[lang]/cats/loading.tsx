import { Skeleton } from "@/components/ui/skeleton";
import { Card, CardContent } from "@/components/ui/card";

export default function CatsLoading() {
	return (
		<main className="w-full flex justify-center py-10 bg-gray-50">
			<div className="container max-w-7xl px-4 sm:px-6 lg:px-8">
				{/* Header Section */}
				<div className="flex justify-between items-center mb-6">
					<Skeleton className="h-8 w-48" />
				</div>

				{/* Description */}
				<div className="mb-6 text-center sm:text-left">
					<Skeleton className="h-4 w-full max-w-2xl mx-auto sm:mx-0" />
				</div>

				{/* Search and Filters Section */}
				<div className="mb-6 space-y-4 sm:space-y-0 sm:flex sm:items-center sm:justify-between sm:gap-4">
					{/* Search Component Skeleton */}
					<div className="w-full sm:w-auto sm:flex-1 sm:max-w-md">
						<Skeleton className="h-10 w-full" />
					</div>

					{/* Filters Component Skeleton */}
					<div className="w-full sm:w-auto">
						<Skeleton className="h-10 w-32" />
					</div>
				</div>

				{/* Cat Cards Grid */}
				<div className="w-full">
					<div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
						{Array.from({ length: 12 }).map((_, i) => (
							<CatCardSkeleton key={i} />
						))}
					</div>

					{/* Pagination Skeleton */}
					<div className="flex justify-center items-center gap-2 mt-8">
						<Skeleton className="h-10 w-20" />
						<div className="flex gap-1">
							{Array.from({ length: 5 }).map((_, i) => (
								<Skeleton key={i} className="h-10 w-10" />
							))}
						</div>
						<Skeleton className="h-10 w-20" />
					</div>
				</div>
			</div>
		</main>
	);
}

function CatCardSkeleton() {
	return (
		<Card className="overflow-hidden h-full rounded-2xl border border-gray-200 bg-white shadow-sm">
			{/* Image Section */}
			<div className="relative overflow-hidden">
				<Skeleton className="h-60 w-full" />
				{/* Heart Icon Skeleton */}
				<div className="absolute top-4 right-4">
					<Skeleton className="h-10 w-10 rounded-full" />
				</div>
			</div>

			{/* Card Content */}
			<CardContent className="p-6 grow">
				{/* Title and Avatar Row */}
				<div className="flex justify-between items-start mb-2">
					<Skeleton className="h-6 w-32" />
					<Skeleton className="h-8 w-8 rounded-full" />
				</div>

				{/* Age and Breed */}
				<Skeleton className="h-4 w-24 mb-4" />

				{/* Description */}
				<div className="space-y-2 mb-4">
					<Skeleton className="h-4 w-full" />
					<Skeleton className="h-4 w-3/4" />
				</div>

				{/* Location */}
				<div className="flex items-center mb-3">
					<Skeleton className="h-4 w-4 mr-1 flex-shrink-0" />
					<Skeleton className="h-4 w-28" />
				</div>
			</CardContent>
		</Card>
	);
}
