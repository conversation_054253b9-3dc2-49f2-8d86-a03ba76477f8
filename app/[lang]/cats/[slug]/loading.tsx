import { Skeleton } from "@/components/ui/skeleton";

export default function CatDetailsLoading() {
	return (
		<main
			className="min-h-screen bg-white"
			role="main"
			aria-label="Loading cat details"
		>
			<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
				<div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
					{/* Left Column - Images */}
					<div className="lg:col-span-2">
						{/* Main Image Gallery */}
						<div
							className="relative mb-6"
							role="img"
							aria-label="Loading cat gallery"
						>
							{/* Main Carousel Image */}
							<div className="relative">
								<div className="relative aspect-[4/3] rounded-lg overflow-hidden bg-muted">
									<Skeleton className="w-full h-full animate-pulse" />
									{/* Navigation buttons skeleton - RTL aware positioning */}
									<div className="absolute ltr:left-4 rtl:right-4 top-1/2 -translate-y-1/2">
										<Skeleton className="h-10 w-10 rounded-full min-h-[44px] min-w-[44px]" />
									</div>
									<div className="absolute ltr:right-4 rtl:left-4 top-1/2 -translate-y-1/2">
										<Skeleton className="h-10 w-10 rounded-full min-h-[44px] min-w-[44px]" />
									</div>
									{/* Image counter skeleton - RTL aware positioning */}
									<div className="absolute bottom-4 ltr:right-4 rtl:left-4">
										<Skeleton className="h-6 w-16 rounded-full" />
									</div>
								</div>
							</div>

							{/* Thumbnail Navigation */}
							<div
								className="flex gap-2 overflow-x-auto pb-2 scrollbar-hide mt-4"
								role="tablist"
								aria-label="Loading image thumbnails"
							>
								{Array.from({ length: 4 }).map((_, index) => (
									<Skeleton
										key={index}
										className="h-14 w-14 sm:h-16 sm:w-16 md:h-20 md:w-20 rounded-md shrink-0 min-h-[44px] min-w-[44px]"
										role="tab"
										aria-label={`Loading thumbnail ${index + 1}`}
									/>
								))}
							</div>
						</div>

						{/* Cat Details Card */}
						<div
							className="bg-white border border-gray-200 rounded-2xl p-6 mt-7"
							role="region"
							aria-label="Loading cat details"
						>
							<Skeleton
								className="h-8 w-48 mb-4"
								aria-label="Loading cat details title"
							/>
							<div
								className="space-y-3"
								role="group"
								aria-label="Loading description text"
							>
								<Skeleton className="h-5 w-full animate-pulse" />
								<Skeleton className="h-5 w-full animate-pulse" />
								<Skeleton className="h-5 w-3/4 animate-pulse" />
								<Skeleton className="h-5 w-full animate-pulse" />
								<Skeleton className="h-5 w-2/3 animate-pulse" />
							</div>
						</div>

						{/* Story Section Skeleton */}
						<div
							className="bg-white border border-gray-200 rounded-2xl p-6 mt-7"
							role="region"
							aria-label="Loading cat story"
						>
							<Skeleton
								className="h-8 w-32 mb-4"
								aria-label="Loading story title"
							/>
							<div
								className="space-y-3"
								role="group"
								aria-label="Loading story text"
							>
								<Skeleton className="h-5 w-full animate-pulse" />
								<Skeleton className="h-5 w-full animate-pulse" />
								<Skeleton className="h-5 w-4/5 animate-pulse" />
								<Skeleton className="h-5 w-full animate-pulse" />
								<Skeleton className="h-5 w-3/4 animate-pulse" />
							</div>
						</div>
					</div>

					{/* Right Column - Info & Actions */}
					<div className="space-y-6">
						{/* Main Info Card */}
						<div
							className="bg-white border border-gray-200 rounded-2xl p-6 sticky top-24"
							role="complementary"
							aria-label="Loading cat information and actions"
						>
							<div className="flex items-start justify-between mb-4">
								<div className="flex-1">
									{/* Cat name */}
									<Skeleton
										className="h-9 w-40 mb-2 animate-pulse"
										aria-label="Loading cat name"
									/>
									{/* Location - RTL aware spacing */}
									<div className="flex items-center ltr:space-x-2 rtl:space-x-reverse rtl:space-x-2">
										<Skeleton className="h-4 w-4 animate-pulse" />
										<Skeleton className="h-4 w-32 animate-pulse" />
									</div>
								</div>
							</div>

							{/* Cat characteristics */}
							<div
								className="space-y-4 mb-6"
								role="group"
								aria-label="Loading cat characteristics"
							>
								{/* Age, Gender, Breed row */}
								<div className="grid grid-cols-2 gap-4">
									<div>
										<Skeleton className="h-4 w-12 mb-1 animate-pulse" />
										<Skeleton className="h-5 w-16 animate-pulse" />
									</div>
									<div>
										<Skeleton className="h-4 w-16 mb-1 animate-pulse" />
										<Skeleton className="h-5 w-20 animate-pulse" />
									</div>
								</div>
								<div className="grid grid-cols-2 gap-4">
									<div>
										<Skeleton className="h-4 w-14 mb-1 animate-pulse" />
										<Skeleton className="h-5 w-24 animate-pulse" />
									</div>
									<div>
										<Skeleton className="h-4 w-18 mb-1 animate-pulse" />
										<Skeleton className="h-5 w-20 animate-pulse" />
									</div>
								</div>
							</div>

							{/* Health status badges */}
							<div
								className="flex flex-wrap gap-2 mb-6"
								role="group"
								aria-label="Loading health status badges"
							>
								<Skeleton className="h-6 w-20 rounded-full animate-pulse" />
								<Skeleton className="h-6 w-24 rounded-full animate-pulse" />
								<Skeleton className="h-6 w-28 rounded-full animate-pulse" />
							</div>

							{/* Action buttons */}
							<div
								className="space-y-3 mb-6"
								role="group"
								aria-label="Loading action buttons"
							>
								{/* Primary action button */}
								<Skeleton
									className="h-12 w-full rounded-xl min-h-[44px] animate-pulse"
									aria-label="Loading primary action button"
								/>
								{/* Secondary buttons row */}
								<div className="flex gap-2">
									<Skeleton
										className="h-12 flex-1 rounded-xl min-h-[44px] animate-pulse"
										aria-label="Loading secondary action button"
									/>
									<Skeleton
										className="h-12 w-12 rounded-xl min-h-[44px] min-w-[44px] animate-pulse"
										aria-label="Loading favorite button"
									/>
									<Skeleton
										className="h-12 w-12 rounded-xl min-h-[44px] min-w-[44px] animate-pulse"
										aria-label="Loading share button"
									/>
								</div>
							</div>

							{/* Caretaker info section */}
							<div
								className="border-t border-gray-200 pt-6"
								role="region"
								aria-label="Loading caretaker information"
							>
								<Skeleton
									className="h-5 w-24 mb-4 animate-pulse"
									aria-label="Loading caretaker section title"
								/>
								{/* RTL aware spacing for caretaker info */}
								<div className="flex items-center ltr:space-x-3 rtl:space-x-reverse rtl:space-x-3">
									{/* Avatar */}
									<Skeleton
										className="h-12 w-12 rounded-full animate-pulse"
										aria-label="Loading caretaker avatar"
									/>
									<div className="flex-1 min-w-0">
										{/* Name */}
										<Skeleton
											className="h-5 w-32 mb-1 animate-pulse"
											aria-label="Loading caretaker name"
										/>
										{/* Location */}
										<Skeleton
											className="h-4 w-28 animate-pulse"
											aria-label="Loading caretaker location"
										/>
									</div>
									{/* View profile button */}
									<Skeleton
										className="h-11 w-24 rounded-xl min-h-[44px] flex-shrink-0 animate-pulse"
										aria-label="Loading view profile button"
									/>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</main>
	);
}
