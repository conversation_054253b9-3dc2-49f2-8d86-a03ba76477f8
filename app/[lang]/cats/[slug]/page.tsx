import { notFound } from "next/navigation";
import { api } from "@/lib/trpc/server";
import { getTranslations } from "next-intl/server";
import { setRequestLocale } from "next-intl/server";
import type { Locale } from "@/lib/i18n/routing";
import { CatDetailsClient } from "./cat-details-client";
import { CatDetail } from "@/lib/types/cat";

// Fetch cat data from the server using tRPC
async function getCat(slug: string) {
	return api.cats.getBySlug(slug);
}

export async function generateMetadata({
	params,
}: {
	params: Promise<{ slug: string; lang: Locale }>;
}) {
	// Await the params object
	const resolvedParams = await params;
	const cat = await getCat(resolvedParams.slug);
	const t = await getTranslations({
		locale: resolvedParams.lang,
		namespace: "cats",
	});
	const common = await getTranslations({
		locale: resolvedParams.lang,
		namespace: "common",
	});

	if (!cat) {
		return {
			title: t("notFound"),
			description: t("notFoundDescription"),
		};
	}

	return {
		title: `${cat.name} - ${t("adoption")} | ${common("appName")}`,
		description:
			cat.description?.substring(0, 160) ||
			`${t("adoptNow")} ${cat.name} ${t("today")}!`,
	};
}

export default async function CatDetailsPage({
	params,
}: {
	params: Promise<{ slug: string; lang: Locale }>;
}) {
	// Await the params object
	const resolvedParams = await params;
	const cat = await getCat(resolvedParams.slug);

	// Enable static rendering
	setRequestLocale(resolvedParams.lang);

	if (!cat) {
		notFound();
	}

	return (
		<CatDetailsClient
			cat={cat as CatDetail}
			lang={resolvedParams.lang}
			slug={resolvedParams.slug}
		/>
	);
}
