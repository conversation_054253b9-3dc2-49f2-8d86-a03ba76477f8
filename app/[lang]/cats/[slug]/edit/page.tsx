import { notFound, redirect } from "next/navigation";
import { auth } from "@/lib/auth/auth";
import { db } from "@/lib/db";
import { users } from "@/lib/db/schema";
import { eq } from "drizzle-orm";
import { headers } from "next/headers";
import { api } from "@/lib/trpc/server";
import { CatForm } from "@/components/cat-form";
import { getTranslations } from "next-intl/server";
import { setRequestLocale } from "next-intl/server";
import type { Locale } from "@/lib/i18n/routing";

export async function generateMetadata({
	params,
}: {
	params: Promise<{ slug: string; lang: Locale }>;
}) {
	const { lang } = await params;
	const t = await getTranslations({ locale: lang, namespace: "cats.edit" });
	const common = await getTranslations({ locale: lang, namespace: "common" });

	return {
		title: `${t("title") || "Edit Cat"} - ${common("appName")}`,
		description: t("description") || "Edit cat listing",
	};
}

export default async function EditCatPage({
	params,
}: {
	params: Promise<{ slug: string; lang: Locale }>;
}) {
	const { lang, slug } = await params;

	// Enable static rendering
	setRequestLocale(lang);

	// Get authenticated user
	const session = await auth.api.getSession({
		headers: await headers(),
	});

	if (!session) {
		redirect(`/${lang}/auth/login?callbackUrl=/${lang}/cats/${slug}/edit`);
	}

	// Get user details from database
	const userId = parseInt(session.user.id);
	const user = await db.query.users.findFirst({
		where: eq(users.id, userId),
	});

	if (!user) {
		redirect(`/${lang}/auth/login`);
	}

	// Check if user is authorized (rescuer, clinic, or admin)
	if (
		user.role !== "rescuer" &&
		user.role !== "clinic" &&
		user.role !== "admin"
	) {
		redirect(`/${lang}/cats/${slug}`);
	}

	// Get cat data using slug
	const cat = await api.cats.getBySlug(slug);

	if (!cat) {
		notFound();
	}

	// Check if user owns this cat or is admin
	const isOwner = cat.userId === userId;
	const isAdmin = user.role === "admin";

	if (!isOwner && !isAdmin) {
		redirect(`/${lang}/cats/${slug}`);
	}

	return (
		<main className="container py-10 flex flex-col items-center">
			<div className="w-full max-w-3xl">
				<CatForm cat={cat} />
			</div>
		</main>
	);
}
