import { notFound } from "next/navigation";
import { api } from "@/lib/trpc/server";
import { getTranslations } from "next-intl/server";
import { setRequestLocale } from "next-intl/server";
import type { Locale } from "@/lib/i18n/routing";
import { PublicProfileHeader } from "@/components/profile/public-profile-header";
import { PublicProfileTabs } from "@/components/profile/public-profile-tabs";

// Fetch user data from the server using tRPC
async function getUser(slug: string) {
	return await api.users.getPublicProfile(slug);
}

export async function generateMetadata({
	params,
}: {
	params: Promise<{ slug: string; lang: Locale }>;
}) {
	const { slug, lang } = await params;
	const user = await getUser(slug);
	const common = await getTranslations({ locale: lang, namespace: "common" });
	const profileT = await getTranslations({
		locale: lang,
		namespace: "profile.public",
	});

	if (!user) {
		return {
			title: profileT("notFound.title"),
			description: profileT("notFound.description"),
		};
	}

	const roleDisplayName = profileT(`roles.${user.role}`);
	const description =
		user.bio?.substring(0, 160) ||
		`${user.name} is a ${roleDisplayName.toLowerCase()} with ${user.stats.catsListed} cats listed and ${user.stats.adoptions} successful adoptions.`;

	return {
		title: `${user.name} - ${roleDisplayName} | ${common("appName")}`,
		description,
	};
}

export default async function UserProfilePage({
	params,
}: {
	params: Promise<{ slug: string; lang: Locale }>;
}) {
	const { slug, lang } = await params;

	// Enable static rendering
	setRequestLocale(lang);

	const user = await getUser(slug);

	if (!user) {
		notFound();
	}

	return (
		<>
			<main className="container mx-auto px-4 py-8 max-w-6xl">
				<PublicProfileHeader user={user} />
				<PublicProfileTabs user={user} />
			</main>
		</>
	);
}
