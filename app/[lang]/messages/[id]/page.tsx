import { notFound } from "next/navigation";
import { api } from "@/lib/trpc/server";
import { getTranslations } from "next-intl/server";
import { setRequestLocale } from "next-intl/server";
import type { Locale } from "@/lib/i18n/routing";
import { ChatClient } from "./chat-client";

// TypeScript interfaces for the conversation header data
interface ConversationHeaderData {
	with: {
		id: string;
		slug: string;
		name: string;
		image: string | null;
		isVerified: boolean;
	} | null;
	cat: {
		id: string;
		slug: string;
		name: string;
		imageUrl: string;
		userId: string;
	} | null;
}

interface ChatPageProps {
	params: Promise<{
		lang: Locale;
		id: string;
	}>;
}

export async function generateMetadata({
	params,
}: {
	params: Promise<{ lang: Locale; id: string }>;
}) {
	const resolvedParams = await params;
	const { lang, id: chatId } = resolvedParams;

	// Get translations
	const t = await getTranslations({
		locale: lang,
		namespace: "profile.messages",
	});
	const common = await getTranslations({ locale: lang, namespace: "common" });

	// Try to get conversation header data for more specific metadata
	try {
		const headerData = await api.messages.getConversationHeader({ chatId });
		const otherUserName = headerData?.with?.name || t("unknownUser");
		const catName = headerData?.cat?.name;

		let title = `${t("chatWith")} ${otherUserName}`;
		if (catName) {
			title += ` - ${catName}`;
		}
		title += ` - ${common("appName")}`;

		return {
			title,
			description: catName
				? `${t("conversationAbout")} ${catName} ${t("with")} ${otherUserName}`
				: `${t("conversationWith")} ${otherUserName}`,
		};
	} catch (error) {
		// Fallback metadata if conversation not found or access denied
		return {
			title: `${t("title")} - ${common("appName")}`,
			description: t("description"),
		};
	}
}

export default async function ChatPage({ params }: ChatPageProps) {
	const resolvedParams = await params;
	const { lang, id: chatId } = resolvedParams;

	// Enable static rendering
	setRequestLocale(lang);

	// Fetch conversation header data on the server
	const headerData = await api.messages.getConversationHeader({ chatId });

	// Pass the header data to the client component
	return <ChatClient chatId={chatId} headerData={headerData} />;
}
