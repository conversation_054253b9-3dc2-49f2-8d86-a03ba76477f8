import { Skeleton } from "@/components/ui/skeleton";
import { ArrowLeft, Send } from "lucide-react";
import { cn } from "@/lib/utils";

export default function ChatLoading() {
	return (
		<div className="flex flex-col h-full bg-white">
			{/* Chat Header Skeleton */}
			<div className="bg-white border-b border-gray-200 p-5">
				<div className="flex items-center justify-between">
					{/* Mobile Back Button */}
					<div className="md:hidden p-2 hover:bg-gray-100 rounded-lg transition-colors mr-3">
						<ArrowLeft className="w-5 h-5 text-gray-300 animate-pulse" />
					</div>

					{/* User Info Section */}
					<div className="flex items-center space-x-4 flex-1">
						{/* Avatar Skeleton */}
						<Skeleton className="w-12 h-12 rounded-full flex-shrink-0" />

						<div className="flex-1 min-w-0">
							{/* User name and verification */}
							<div className="flex items-center space-x-2 mb-1">
								<Skeleton className="h-5 w-32" />
								<Skeleton className="w-5 h-5 rounded" />
							</div>
							
							{/* Cat info */}
							<div className="flex items-center space-x-2">
								<Skeleton className="w-5 h-5 rounded-full" />
								<Skeleton className="h-4 w-24" />
							</div>
						</div>
					</div>

					{/* Status Menu Skeleton */}
					<Skeleton className="w-8 h-8 rounded-lg" />
				</div>
			</div>

			{/* Messages Area Skeleton */}
			<div className="flex-1 overflow-y-auto p-4 space-y-4">
				{/* Date Separator */}
				<div className="flex justify-center">
					<Skeleton className="h-6 w-20 rounded-full" />
				</div>

				{/* Message Bubbles - Mix of sent and received */}
				{Array.from({ length: 8 }).map((_, index) => {
					const isCurrentUser = index % 3 === 0; // Mix of sent/received messages
					const isLongMessage = index % 4 === 0; // Some longer messages
					
					return (
						<div key={index} className="space-y-2">
							{/* Occasional date separator */}
							{index === 4 && (
								<div className="flex justify-center">
									<Skeleton className="h-6 w-16 rounded-full" />
								</div>
							)}

							{/* Message Bubble */}
							<div
								className={cn(
									"flex",
									isCurrentUser ? "justify-end" : "justify-start"
								)}
							>
								<div
									className={cn(
										"max-w-md px-4 py-2 rounded-2xl space-y-2",
										isCurrentUser
											? "bg-teal-100 ml-12"
											: "bg-gray-100 mr-12"
									)}
								>
									{/* Message content skeleton */}
									<div className="space-y-1">
										<Skeleton 
											className={cn(
												"h-4",
												isLongMessage ? "w-48" : "w-32"
											)} 
										/>
										{isLongMessage && (
											<>
												<Skeleton className="h-4 w-40" />
												<Skeleton className="h-4 w-28" />
											</>
										)}
									</div>

									{/* Message Status for sent messages */}
									{isCurrentUser && (
										<div className="flex justify-end">
											<Skeleton className="w-4 h-4 rounded" />
										</div>
									)}
								</div>
							</div>
						</div>
					);
				})}

				{/* Recent messages with different patterns */}
				<div className="flex justify-start">
					<div className="max-w-md px-4 py-2 rounded-2xl bg-gray-100 mr-12">
						<Skeleton className="h-4 w-20" />
					</div>
				</div>

				<div className="flex justify-end">
					<div className="max-w-md px-4 py-2 rounded-2xl bg-teal-100 ml-12 space-y-1">
						<Skeleton className="h-4 w-36" />
						<Skeleton className="h-4 w-24" />
						<div className="flex justify-end">
							<Skeleton className="w-4 h-4 rounded" />
						</div>
					</div>
				</div>
			</div>

			{/* Message Input Area Skeleton */}
			<div className="border-t border-gray-200 p-3 md:p-4 bg-white">
				<div className="flex items-end space-x-2 md:space-x-4">
					{/* Message Input Skeleton */}
					<div className="flex-1 relative">
						<Skeleton 
							className="w-full h-10 md:h-12 rounded-xl"
							style={{
								minHeight: "40px",
							}}
						/>
					</div>

					{/* Send Button Skeleton */}
					<div className="p-2 md:p-3 rounded-xl bg-gray-100 flex-shrink-0">
						<Send className="w-4 h-4 md:w-5 md:h-5 text-gray-300" />
					</div>
				</div>
			</div>
		</div>
	);
}
