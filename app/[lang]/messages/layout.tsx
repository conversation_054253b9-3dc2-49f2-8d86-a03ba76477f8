"use client";

import { ConversationsList } from "@/components/profile/conversations-list";
import type { Locale } from "@/lib/i18n/routing";
import { useParams } from "next/navigation";
import { useTranslations } from "next-intl";
import { cn } from "@/lib/utils";

interface MessagesLayoutProps {
	children: React.ReactNode;
	params: Promise<{ lang: Locale }>;
}

export default function MessagesLayout({ children }: MessagesLayoutProps) {
	const urlParams = useParams();
	const t = useTranslations("profile.messages");

	// Check if we're viewing a specific conversation (has ID in URL)
	const isConversationSelected = urlParams.id !== undefined;

	return (
		<div className="flex h-[calc(100vh-4rem)] overflow-hidden bg-gray-50">
			{/* Conversations sidebar */}
			<div
				className={cn(
					"bg-white border-r border-gray-200 flex flex-col transition-all duration-300",
					// Mobile: full width when showing list, hidden when showing chat
					"w-full md:w-96",
					// Hide on mobile when chat is selected
					isConversationSelected && "hidden md:flex"
				)}
			>
				{/* Header */}
				<div className="p-6 border-b border-gray-200">
					<div className="flex items-center justify-between mb-4">
						<h1 className="text-2xl font-display font-bold text-gray-900">
							{t("title")}
						</h1>
					</div>
				</div>

				{/* Conversations List */}
				<div className="flex-1 overflow-y-auto">
					<ConversationsList />
				</div>
			</div>

			{/* Chat area */}
			<div
				className={cn(
					"flex flex-col transition-all duration-300 bg-white",
					// Mobile: full width when showing chat, hidden when showing list
					"w-full md:flex-1",
					// Show on mobile when chat is selected
					!isConversationSelected ? "hidden md:flex" : "flex"
				)}
			>
				{children}
			</div>
		</div>
	);
}
