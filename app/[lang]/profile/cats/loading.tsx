import { Skeleton } from "@/components/ui/skeleton";

export default function ProfileCatsLoading() {
	return (
		<div className="space-y-6">
			{/* Header */}
			<div>
				<Skeleton className="h-8 w-64 mb-2" />
				<Skeleton className="h-4 w-96" />
			</div>

			{/* Action Bar */}
			<div className="flex flex-col sm:flex-row justify-between gap-4">
				<div className="flex gap-2">
					<Skeleton className="h-10 w-24" />
					<Skeleton className="h-10 w-28" />
				</div>
				<Skeleton className="h-10 w-32" />
			</div>

			{/* Stats Cards */}
			<div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
				{Array.from({ length: 4 }).map((_, i) => (
					<div key={i} className="p-4 border rounded-lg space-y-2">
						<Skeleton className="h-4 w-16" />
						<Skeleton className="h-8 w-12" />
						<Skeleton className="h-3 w-20" />
					</div>
				))}
			</div>

			{/* Cat List */}
			<div className="space-y-4">
				{Array.from({ length: 6 }).map((_, i) => (
					<div key={i} className="border rounded-lg p-4">
						<div className="flex gap-4">
							{/* Cat Image */}
							<Skeleton className="w-20 h-20 rounded-lg flex-shrink-0" />
							
							{/* Cat Info */}
							<div className="flex-1 space-y-2">
								<div className="flex justify-between items-start">
									<div className="space-y-1">
										<Skeleton className="h-5 w-32" />
										<Skeleton className="h-4 w-24" />
									</div>
									<div className="flex gap-2">
										<Skeleton className="h-6 w-16" />
										<Skeleton className="h-6 w-20" />
									</div>
								</div>
								<Skeleton className="h-4 w-full" />
								<div className="flex gap-2">
									<Skeleton className="h-8 w-16" />
									<Skeleton className="h-8 w-16" />
									<Skeleton className="h-8 w-16" />
								</div>
							</div>
						</div>
					</div>
				))}
			</div>

			{/* Pagination */}
			<div className="flex justify-center">
				<div className="flex gap-2">
					<Skeleton className="h-10 w-10" />
					<Skeleton className="h-10 w-10" />
					<Skeleton className="h-10 w-10" />
					<Skeleton className="h-10 w-10" />
				</div>
			</div>
		</div>
	);
}
