import { redirect } from "@/lib/i18n/navigation";
import { getTranslations } from "next-intl/server";
import { setRequestLocale } from "next-intl/server";
import type { Locale } from "@/lib/i18n/routing";
import { api } from "@/lib/trpc/server";
import { ListedCats } from "@/components/profile/listed-cats";

interface ProfileCatsPageProps {
	params: Promise<{
		lang: Locale;
	}>;
}

export async function generateMetadata({
	params,
}: {
	params: Promise<{ lang: Locale }>;
}) {
	const resolvedParams = await params;
	const t = await getTranslations({
		locale: resolvedParams.lang,
		namespace: "profile",
	});

	return {
		title: `${t("myListedCats")} - ${t("title")}`,
		description: t("listedCatsDescription"),
	};
}

export default async function ProfileCatsPage({
	params,
}: ProfileCatsPageProps) {
	const resolvedParams = await params;

	// Enable static rendering
	setRequestLocale(resolvedParams.lang);

	// Fetch user profile to check permissions (authentication is handled by layout)
	const user = await api.users.getProfile();

	// Check if user has permission to access this page
	if (user.role !== "rescuer" && user.role !== "clinic") {
		redirect({
			href: "/profile/favorites",
			locale: resolvedParams.lang,
		});
	}

	return (
		<div className="space-y-6">
			<ListedCats />
		</div>
	);
}
