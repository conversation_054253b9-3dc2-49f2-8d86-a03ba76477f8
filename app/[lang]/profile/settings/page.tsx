import { getTranslations } from "next-intl/server";
import { setRequestLocale } from "next-intl/server";
import type { Locale } from "@/lib/i18n/routing";
import { api } from "@/lib/trpc/server";
import { SettingsTab } from "@/components/profile/shared/settings-tab";

interface ProfileSettingsPageProps {
	params: Promise<{
		lang: Locale;
	}>;
}

export async function generateMetadata({
	params,
}: {
	params: Promise<{ lang: Locale }>;
}) {
	const resolvedParams = await params;
	const t = await getTranslations({
		locale: resolvedParams.lang,
		namespace: "profile.settings",
	});

	return {
		title: `${t("title")} - ${t("profileInfo")}`,
		description: t("description"),
	};
}

export default async function ProfileSettingsPage({
	params,
}: ProfileSettingsPageProps) {
	const resolvedParams = await params;

	// Enable static rendering
	setRequestLocale(resolvedParams.lang);

	// Fetch user profile using tRPC (authentication is handled by layout)
	const user = await api.users.getProfile();

	return (
		<div className="space-y-6">
			<SettingsTab user={user} />
		</div>
	);
}
