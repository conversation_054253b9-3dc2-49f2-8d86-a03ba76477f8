import { Skeleton } from "@/components/ui/skeleton";

export default function ProfileSettingsLoading() {
	return (
		<div className="space-y-6">
			{/* Header */}
			<div>
				<Skeleton className="h-8 w-48 mb-2" />
				<Skeleton className="h-4 w-80" />
			</div>

			{/* Settings Form */}
			<div className="space-y-8">
				{/* Profile Information Section */}
				<div className="border rounded-lg p-6 space-y-6">
					<div>
						<Skeleton className="h-6 w-40 mb-2" />
						<Skeleton className="h-4 w-64" />
					</div>

					{/* Profile Picture */}
					<div className="flex items-center gap-6">
						<Skeleton className="w-20 h-20 rounded-full" />
						<div className="space-y-2">
							<Skeleton className="h-4 w-32" />
							<Skeleton className="h-9 w-28" />
						</div>
					</div>

					{/* Form Fields */}
					<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
						<div className="space-y-2">
							<Skeleton className="h-4 w-16" />
							<Skeleton className="h-10 w-full" />
						</div>
						<div className="space-y-2">
							<Skeleton className="h-4 w-20" />
							<Skeleton className="h-10 w-full" />
						</div>
						<div className="space-y-2 md:col-span-2">
							<Skeleton className="h-4 w-12" />
							<Skeleton className="h-20 w-full" />
						</div>
						<div className="space-y-2">
							<Skeleton className="h-4 w-16" />
							<Skeleton className="h-10 w-full" />
						</div>
						<div className="space-y-2">
							<Skeleton className="h-4 w-20" />
							<Skeleton className="h-10 w-full" />
						</div>
						<div className="space-y-2">
							<Skeleton className="h-4 w-24" />
							<Skeleton className="h-10 w-full" />
						</div>
					</div>

					{/* Save Button */}
					<div className="flex justify-end">
						<Skeleton className="h-10 w-32" />
					</div>
				</div>

				{/* Password Section */}
				<div className="border rounded-lg p-6 space-y-6">
					<div>
						<Skeleton className="h-6 w-32 mb-2" />
						<Skeleton className="h-4 w-56" />
					</div>

					<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
						<div className="space-y-2">
							<Skeleton className="h-4 w-28" />
							<Skeleton className="h-10 w-full" />
						</div>
						<div className="space-y-2">
							<Skeleton className="h-4 w-32" />
							<Skeleton className="h-10 w-full" />
						</div>
					</div>

					{/* Update Password Button */}
					<div className="flex justify-end">
						<Skeleton className="h-10 w-36" />
					</div>
				</div>

				{/* Account Actions Section */}
				<div className="border rounded-lg p-6 space-y-4">
					<div>
						<Skeleton className="h-6 w-36 mb-2" />
						<Skeleton className="h-4 w-72" />
					</div>

					<div className="flex gap-4">
						<Skeleton className="h-10 w-32" />
						<Skeleton className="h-10 w-36" />
					</div>
				</div>
			</div>
		</div>
	);
}
