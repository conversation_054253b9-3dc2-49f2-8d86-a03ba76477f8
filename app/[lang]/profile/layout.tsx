import { api } from "@/lib/trpc/server";
import { setRequestLocale } from "next-intl/server";
import type { Locale } from "@/lib/i18n/routing";
import { ProfileLayoutClient } from "@/components/profile/shared/profile-layout-client";

interface ProfileLayoutProps {
	children: React.ReactNode;
	params: Promise<{
		lang: Locale;
	}>;
}

export default async function ProfileLayout({
	children,
	params,
}: ProfileLayoutProps) {
	const resolvedParams = await params;

	// Enable static rendering
	setRequestLocale(resolvedParams.lang);

	// Fetch user profile using tRPC
	const user = await api.users.getProfile();

	return <ProfileLayoutClient user={user}>{children}</ProfileLayoutClient>;
}
