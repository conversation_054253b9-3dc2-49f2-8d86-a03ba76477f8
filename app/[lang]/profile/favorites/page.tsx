import { getTranslations } from "next-intl/server";
import { setRequestLocale } from "next-intl/server";
import type { Locale } from "@/lib/i18n/routing";
import { FavoriteCats } from "@/components/profile/favorite-cats";

interface ProfileFavoritesPageProps {
	params: Promise<{
		lang: Locale;
	}>;
}

export async function generateMetadata({
	params,
}: {
	params: Promise<{ lang: Locale }>;
}) {
	const resolvedParams = await params;
	const t = await getTranslations({
		locale: resolvedParams.lang,
		namespace: "profile",
	});

	return {
		title: `${t("favorites")} - ${t("title")}`,
		description: t("favoritesDescription"),
	};
}

export default async function ProfileFavoritesPage({
	params,
}: ProfileFavoritesPageProps) {
	const resolvedParams = await params;

	// Enable static rendering
	setRequestLocale(resolvedParams.lang);

	return (
		<div className="space-y-6">
			<FavoriteCats />
		</div>
	);
}
