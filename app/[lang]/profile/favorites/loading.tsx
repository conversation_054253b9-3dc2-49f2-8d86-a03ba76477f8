import { Skeleton } from "@/components/ui/skeleton";

export default function ProfileFavoritesLoading() {
	return (
		<div className="space-y-6">
			{/* Header */}
			<div>
				<Skeleton className="h-8 w-64 mb-2" />
				<Skeleton className="h-4 w-96" />
			</div>

			{/* Content Loading */}
			<div className="space-y-6">
				{/* Filter/Search Bar */}
				<div className="flex flex-col sm:flex-row gap-4">
					<Skeleton className="h-10 flex-1" />
					<Skeleton className="h-10 w-32" />
				</div>

				{/* Grid of Cat Cards */}
				<div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
					{Array.from({ length: 8 }).map((_, i) => (
						<div key={i} className="space-y-3">
							{/* Cat Image */}
							<Skeleton className="aspect-square w-full rounded-lg" />
							
							{/* Cat Info */}
							<div className="space-y-2">
								<Skeleton className="h-5 w-3/4" />
								<Skeleton className="h-4 w-1/2" />
								<div className="flex gap-2">
									<Skeleton className="h-6 w-16" />
									<Skeleton className="h-6 w-20" />
								</div>
							</div>
						</div>
					))}
				</div>

				{/* Pagination */}
				<div className="flex justify-center">
					<div className="flex gap-2">
						<Skeleton className="h-10 w-10" />
						<Skeleton className="h-10 w-10" />
						<Skeleton className="h-10 w-10" />
						<Skeleton className="h-10 w-10" />
					</div>
				</div>
			</div>
		</div>
	);
}
