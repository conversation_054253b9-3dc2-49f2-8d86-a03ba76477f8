import { Skeleton } from "@/components/ui/skeleton";

export default function ProfileServicesLoading() {
	return (
		<div className="space-y-6">
			{/* Header */}
			<div>
				<Skeleton className="h-8 w-48 mb-2" />
				<Skeleton className="h-4 w-80" />
			</div>

			{/* Action Bar */}
			<div className="flex justify-between items-center">
				<Skeleton className="h-4 w-32" />
				<Skeleton className="h-10 w-32" />
			</div>

			{/* Services Grid */}
			<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
				{Array.from({ length: 6 }).map((_, i) => (
					<div key={i} className="border rounded-lg">
						{/* Card Content */}
						<div className="p-6 space-y-4">
							{/* Service Header */}
							<div className="flex justify-between items-start">
								<Skeleton className="h-6 w-32" />
								<div className="flex items-center gap-2">
									<Skeleton className="h-5 w-12" />
									<Skeleton className="h-4 w-16" />
								</div>
							</div>

							{/* Badges */}
							<div className="flex flex-wrap gap-2">
								<Skeleton className="h-6 w-24" />
								<Skeleton className="h-6 w-16" />
							</div>

							{/* Description */}
							<div className="space-y-2">
								<Skeleton className="h-4 w-full" />
								<Skeleton className="h-4 w-3/4" />
								<Skeleton className="h-4 w-1/2" />
							</div>
						</div>

						{/* Card Footer */}
						<div className="px-6 py-4 pt-0 flex gap-2">
							<Skeleton className="h-9 flex-1" />
							<Skeleton className="h-9 w-9" />
						</div>
					</div>
				))}
			</div>

			{/* Empty State Alternative (if no services) */}
			{/* This would be shown conditionally based on data */}
			<div className="hidden text-center py-12 space-y-4">
				<Skeleton className="h-6 w-48 mx-auto" />
				<Skeleton className="h-4 w-80 mx-auto" />
				<Skeleton className="h-10 w-32 mx-auto" />
			</div>
		</div>
	);
}
