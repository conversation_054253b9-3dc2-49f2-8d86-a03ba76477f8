import { Metadata } from "next";
import { notFound, redirect } from "next/navigation";
import { getTranslations } from "next-intl/server";
import { AddServiceClient } from "@/components/profile/add-service-client";
import { headers } from "next/headers";
import { auth } from "@/lib/auth/auth";

export async function generateMetadata({
	params,
}: {
	params: Promise<{ lang: string }>;
}): Promise<Metadata> {
	const { lang } = await params;
	const t = await getTranslations({ locale: lang, namespace: "services" });

	return {
		title: t("addService.title"),
		description: t("addService.description"),
	};
}

export default async function AddServicePage({
	params,
}: {
	params: Promise<{ lang: string }>;
}) {
	const { lang } = await params;
	const session = await auth.api.getSession({
		headers: await headers(),
	});

	// Check if user is authenticated
	if (!session?.user) {
		redirect(`/${lang}/auth/signin`);
	}

	// Check if user has clinic role
	if (session.user.role !== "clinic") {
		notFound();
	}

	const t = await getTranslations({ locale: lang, namespace: "services" });

	return (
		<div className="container mx-auto px-4 py-8">
			<div className="max-w-4xl mx-auto">
				<div className="mb-8">
					<h1 className="text-3xl font-bold text-gray-900 dark:text-white">
						{t("addService.title")}
					</h1>
					<p className="mt-2 text-gray-600 dark:text-gray-400">
						{t("addService.description")}
					</p>
				</div>

				<AddServiceClient />
			</div>
		</div>
	);
}
