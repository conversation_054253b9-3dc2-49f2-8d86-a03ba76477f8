import { redirect } from "@/lib/i18n/navigation";
import { api } from "@/lib/trpc/server";
import { setRequestLocale } from "next-intl/server";
import type { Locale } from "@/lib/i18n/routing";

// This page redirects to the appropriate profile sub-route
// No metadata needed as this is a redirect-only page

export default async function ProfilePage({
	params,
}: {
	params: Promise<{ lang: Locale }>;
}) {
	const { lang } = await params;

	// Enable static rendering
	setRequestLocale(lang);

	// Fetch user profile using tRPC (authentication is handled by layout)
	const user = await api.users.getProfile();

	// Redirect to the appropriate default sub-route based on user role
	switch (user.role) {
		case "rescuer":
		case "clinic":
			// Rescuers and clinics start with their listed cats
			redirect({
				href: "/profile/cats",
				locale: lang,
			});
		case "adopter":
		default:
			// Adopters start with their favorites
			redirect({
				href: "/profile/favorites",
				locale: lang,
			});
	}
}
