import { redirect } from "@/lib/i18n/navigation";
import { api } from "@/lib/trpc/server";
import { getTranslations } from "next-intl/server";
import { setRequestLocale } from "next-intl/server";
import type { Locale } from "@/lib/i18n/routing";
import { ClinicProfileForm } from "@/components/profile/clinic/clinic-profile-form";

interface ClinicPageProps {
	params: Promise<{
		lang: Locale;
	}>;
}

export async function generateMetadata({
	params,
}: {
	params: Promise<{ lang: Locale }>;
}) {
	const resolvedParams = await params;
	const t = await getTranslations({
		locale: resolvedParams.lang,
		namespace: "profile.clinic",
	});

	return {
		title: `${t("profileTitle")} - ${t("title")}`,
		description: t("profileDescription"),
	};
}

export default async function ClinicPage({ params }: ClinicPageProps) {
	const resolvedParams = await params;

	// Enable static rendering
	setRequestLocale(resolvedParams.lang);

	// Fetch user profile using tRPC (authentication is handled by layout)
	const user = await api.users.getProfile();
	const t = await getTranslations("profile.clinic");

	// Check if user has permission to access this page
	if (user.role !== "clinic") {
		redirect({
			href: "/profile/favorites",
			locale: resolvedParams.lang,
		});
	}

	// Try to fetch existing clinic profile
	let clinicProfile = null;
	try {
		clinicProfile = await api.clinics.getProfile();
	} catch (error) {
		// Profile doesn't exist yet, which is fine for new clinics
		console.log("No clinic profile found, user can create one");
	}

	return (
		<div className="space-y-6">
			<ClinicProfileForm user={user} existingProfile={clinicProfile} />
		</div>
	);
}
