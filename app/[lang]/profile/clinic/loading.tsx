import { Skeleton } from "@/components/ui/skeleton";

export default function ClinicLoading() {
	return (
		<div className="space-y-6">
			{/* Header */}
			<div>
				<Skeleton className="h-8 w-64 mb-2" />
				<Skeleton className="h-4 w-96" />
			</div>

			{/* Basic Information Card */}
			<div className="bg-white border border-gray-200 rounded-2xl p-6">
				<div className="flex items-center mb-4">
					<Skeleton className="w-5 h-5 mr-2" />
					<Skeleton className="h-6 w-40" />
				</div>
				
				<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
					<div className="space-y-2">
						<Skeleton className="h-4 w-24" />
						<Skeleton className="h-12 w-full" />
					</div>
					<div className="space-y-2">
						<Skeleton className="h-4 w-16" />
						<Skeleton className="h-12 w-full" />
					</div>
					<div className="space-y-2">
						<Skeleton className="h-4 w-20" />
						<Skeleton className="h-12 w-full" />
					</div>
					<div className="space-y-2">
						<Skeleton className="h-4 w-24" />
						<Skeleton className="h-12 w-full" />
					</div>
				</div>

				<div className="mt-4 space-y-2">
					<Skeleton className="h-4 w-20" />
					<Skeleton className="h-12 w-full" />
				</div>

				<div className="mt-4 space-y-2">
					<Skeleton className="h-4 w-28" />
					<Skeleton className="h-24 w-full" />
					<Skeleton className="h-3 w-80" />
				</div>
			</div>

			{/* Contact Information Card */}
			<div className="bg-white border border-gray-200 rounded-2xl p-6">
				<div className="flex items-center mb-4">
					<Skeleton className="w-5 h-5 mr-2" />
					<Skeleton className="h-6 w-48" />
				</div>
				
				<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
					<div className="space-y-2">
						<Skeleton className="h-4 w-16" />
						<Skeleton className="h-12 w-full" />
					</div>
					<div className="space-y-2">
						<Skeleton className="h-4 w-16" />
						<Skeleton className="h-12 w-full" />
					</div>
				</div>

				<div className="mt-4 space-y-2">
					<div className="flex items-center">
						<Skeleton className="w-4 h-4 mr-2" />
						<Skeleton className="h-4 w-20" />
					</div>
					<Skeleton className="h-12 w-full" />
					<Skeleton className="h-3 w-64" />
				</div>
			</div>

			{/* Services Card */}
			<div className="bg-white border border-gray-200 rounded-2xl p-6">
				<div className="flex items-center mb-4">
					<Skeleton className="w-5 h-5 mr-2" />
					<Skeleton className="h-6 w-24" />
				</div>
				
				<div className="space-y-3">
					<Skeleton className="h-4 w-32" />
					<div className="flex flex-wrap gap-2">
						<Skeleton className="h-8 w-20" />
						<Skeleton className="h-8 w-24" />
						<Skeleton className="h-8 w-28" />
						<Skeleton className="h-8 w-32" />
					</div>
					<Skeleton className="h-10 w-32" />
					<Skeleton className="h-3 w-96" />
				</div>
			</div>

			{/* Operating Hours Card */}
			<div className="bg-white border border-gray-200 rounded-2xl p-6">
				<div className="flex items-center mb-4">
					<Skeleton className="w-5 h-5 mr-2" />
					<Skeleton className="h-6 w-36" />
				</div>
				
				<div className="space-y-4">
					{Array.from({ length: 7 }).map((_, index) => (
						<div key={index} className="border border-gray-200 rounded-lg p-4">
							<div className="flex items-center justify-between mb-3">
								<div className="flex items-center space-x-3">
									<Skeleton className="w-4 h-4" />
									<Skeleton className="h-4 w-20" />
								</div>
								<Skeleton className="h-8 w-24" />
							</div>
							<div className="space-y-2">
								<div className="flex items-center space-x-3 bg-gray-50 p-3 rounded-md">
									<Skeleton className="h-8 w-32" />
									<Skeleton className="h-4 w-8" />
									<Skeleton className="h-8 w-32" />
									<Skeleton className="h-8 w-8" />
								</div>
							</div>
						</div>
					))}
				</div>
			</div>

			{/* Submit Button */}
			<div className="flex justify-end">
				<Skeleton className="h-12 w-32" />
			</div>
		</div>
	);
}
