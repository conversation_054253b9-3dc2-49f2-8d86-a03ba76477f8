import { Skeleton } from "@/components/ui/skeleton";

export default function ProfileMessagesLoading() {
	return (
		<div className="space-y-6">
			{/* Header */}
			<div>
				<Skeleton className="h-8 w-48 mb-2" />
				<Skeleton className="h-4 w-80" />
			</div>

			{/* Content Loading */}
			<div className="space-y-6">
				{/* Search Bar */}
				<div className="flex flex-col sm:flex-row gap-4">
					<Skeleton className="h-10 flex-1" />
					<Skeleton className="h-10 w-32" />
				</div>

				{/* Messages List */}
				<div className="space-y-4">
					{Array.from({ length: 6 }).map((_, i) => (
						<div key={i} className="bg-white border border-gray-200 rounded-2xl p-6">
							<div className="flex items-center space-x-4">
								{/* Cat Image */}
								<Skeleton className="w-16 h-16 rounded-xl flex-shrink-0" />
								
								{/* Message Info */}
								<div className="flex-1 min-w-0">
									<div className="flex justify-between items-start mb-2">
										<Skeleton className="h-6 w-32" />
										<Skeleton className="h-4 w-20 flex-shrink-0 ml-2" />
									</div>
									<Skeleton className="h-4 w-48 mb-2" />
									<Skeleton className="h-4 w-64" />
								</div>

								{/* Arrow Icon */}
								<div className="flex-shrink-0">
									<Skeleton className="w-5 h-5" />
								</div>
							</div>
						</div>
					))}
				</div>

				{/* Pagination */}
				<div className="flex justify-center">
					<div className="flex gap-2">
						<Skeleton className="h-10 w-10" />
						<Skeleton className="h-10 w-10" />
						<Skeleton className="h-10 w-10" />
						<Skeleton className="h-10 w-10" />
					</div>
				</div>
			</div>
		</div>
	);
}
