import { getTranslations } from "next-intl/server";
import { setRequestLocale } from "next-intl/server";
import type { Locale } from "@/lib/i18n/routing";
import { Messages } from "@/components/profile/messages";

interface ProfileMessagesPageProps {
	params: Promise<{
		lang: Locale;
	}>;
}

export async function generateMetadata({
	params,
}: {
	params: Promise<{ lang: Locale }>;
}) {
	const resolvedParams = await params;
	const t = await getTranslations({
		locale: resolvedParams.lang,
		namespace: "profile",
	});

	return {
		title: `${t("messages.title")} - ${t("title")}`,
		description: t("messages.description"),
	};
}

export default async function ProfileMessagesPage({
	params,
}: ProfileMessagesPageProps) {
	const resolvedParams = await params;

	// Enable static rendering
	setRequestLocale(resolvedParams.lang);

	return (
		<div className="space-y-6">
			<Messages />
		</div>
	);
}
