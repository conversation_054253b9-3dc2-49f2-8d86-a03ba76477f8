"use client";

import { Link } from "@/lib/i18n/navigation";
import { But<PERSON> } from "@/components/ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { useTranslations } from "next-intl";
import { Cat, Home } from "lucide-react";

export default function NotFound() {
	// Get translations using client-side hook
	const t = useTranslations("notFound");

	return (
		<div className="min-h-[calc(100vh-4rem)] flex items-center justify-center px-4 py-16">
			{/* SEO title workaround because not-found pages don't support metadata */}
			<title>{t("title")}</title>
			<div className="container max-w-2xl mx-auto text-center">
				{/* Cat illustration with 404 */}
				<div className="mb-8 relative">
					<div className="text-8xl font-bold text-muted-foreground/20 select-none">
						404
					</div>
					<div className="absolute inset-0 flex items-center justify-center">
						<div className="bg-primary/10 rounded-full p-6">
							<Cat className="h-16 w-16 text-primary" />
						</div>
					</div>
				</div>

				<Card className="border-none shadow-lg">
					<CardHeader className="pb-4">
						<CardTitle className="text-3xl font-bold mb-2">
							{t("title")}
						</CardTitle>
						<CardDescription className="text-lg text-muted-foreground">
							{t("subtitle")}
						</CardDescription>
					</CardHeader>

					<CardContent className="space-y-6">
						<p className="text-muted-foreground max-w-md mx-auto">
							{t("description")}
						</p>

						{/* Suggestions */}
						<div className="bg-muted/50 rounded-lg p-4 text-left">
							<h3 className="font-semibold mb-3 text-center">
								{t("suggestions.title")}
							</h3>
							<ul className="space-y-2 text-sm text-muted-foreground">
								<li className="flex items-center gap-2">
									<Cat className="h-4 w-4 text-primary" />
									{t("suggestions.browseCats")}
								</li>
								<li className="flex items-center gap-2">
									<Home className="h-4 w-4 text-primary" />
									{t("suggestions.goHome")}
								</li>
							</ul>
						</div>

						{/* Action buttons */}
						<div className="flex flex-col sm:flex-row gap-3 justify-center pt-4">
							<Button asChild size="lg" className="gap-2">
								<Link href="/cats">
									<Cat className="h-4 w-4" />
									{t("buttons.browseCats")}
								</Link>
							</Button>
							<Button
								asChild
								variant="outline"
								size="lg"
								className="gap-2"
							>
								<Link href="/">
									<Home className="h-4 w-4" />
									{t("buttons.goHome")}
								</Link>
							</Button>
						</div>
					</CardContent>
				</Card>
			</div>
		</div>
	);
}
