import type React from "react";
import { Inter } from "next/font/google";
import { ThemeProvider } from "@/components/theme-provider";
import { Toaster } from "@/components/ui/toaster";
import "../globals.css";
import type { Metadata } from "next";
import { Navbar } from "@/components/navbar";
import { hasLocale } from "next-intl";
import { notFound } from "next/navigation";
import { locales } from "@/lib/i18n/routing";
import { getTranslations } from "next-intl/server";
import { setRequestLocale } from "next-intl/server";
import { NextIntlProvider } from "@/lib/i18n/client-provider";
import { TRPCReactProvider } from "@/lib/trpc/react";
import { NuqsAdapter } from "nuqs/adapters/next/app";

const inter = Inter({ subsets: ["latin"] });

export async function generateMetadata({
	params,
}: {
	params: { lang: string };
}): Promise<Metadata> {
	const { lang } = await params;
	const t = await getTranslations({ locale: lang, namespace: "common" });

	return {
		title: t("appName"),
		description: t("tagline"),
	};
}

export async function generateStaticParams() {
	return locales.map((lang) => ({ lang }));
}

export default async function RootLayout({
	children,
	params,
}: {
	children: React.ReactNode;
	params: Promise<{ lang: string }>;
}) {
	// Await the params before using them
	const { lang } = await params;

	// Validate the locale
	if (!hasLocale(locales, lang)) {
		notFound();
	}

	// Enable static rendering
	setRequestLocale(lang);

	// Import the messages for the requested locale
	const messages = (await import(`@/lib/i18n/dictionaries/${lang}.json`))
		.default;

	// Set the HTML lang attribute based on the current locale
	const dir = lang === "ar" ? "rtl" : "ltr";

	return (
		<html lang={lang} dir={dir} suppressHydrationWarning>
			<body className={inter.className}>
				<ThemeProvider
					attribute="class"
					defaultTheme="system"
					enableSystem
					disableTransitionOnChange
				>
					<TRPCReactProvider>
						<NextIntlProvider locale={lang} messages={messages}>
							<NuqsAdapter>
								<div className="flex min-h-screen flex-col">
									<Navbar />
									<main>{children}</main>
								</div>
								<Toaster />
							</NuqsAdapter>
						</NextIntlProvider>
					</TRPCReactProvider>
				</ThemeProvider>
			</body>
		</html>
	);
}
