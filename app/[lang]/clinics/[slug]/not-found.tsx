import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON> } from "@/lib/i18n/navigation";
import { Stethoscope, ArrowLeft } from "lucide-react";
import { getTranslations } from "next-intl/server";

export default async function ClinicNotFound() {
	const t = await getTranslations("clinics");
	const commonT = await getTranslations("common");

	return (
		<main className="container mx-auto px-4 py-16 max-w-2xl">
			<Card className="text-center">
				<CardContent className="p-12">
					<div className="flex justify-center mb-6">
						<div className="h-24 w-24 bg-muted rounded-full flex items-center justify-center">
							<Stethoscope className="h-12 w-12 text-muted-foreground" />
						</div>
					</div>
					
					<h1 className="text-2xl font-bold mb-4">
						{t("notFound")}
					</h1>
					
					<p className="text-muted-foreground mb-8 text-lg">
						{t("notFoundDescription")}
					</p>
					
					<div className="flex flex-col sm:flex-row gap-4 justify-center">
						<Button asChild>
							<Link href="/clinics">
								<ArrowLeft className="h-4 w-4 mr-2" />
								{t("title")}
							</Link>
						</Button>
						
						<Button asChild variant="outline">
							<Link href="/">
								{commonT("navigation.home")}
							</Link>
						</Button>
					</div>
				</CardContent>
			</Card>
		</main>
	);
}
