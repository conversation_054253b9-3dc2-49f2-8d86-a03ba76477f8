import Link from "next/link";
import { RegisterForm } from "@/components/register-form";
import {
	<PERSON>,
	CardContent,
	CardDescription,
	Card<PERSON>ooter,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { getTranslations } from "next-intl/server";
import { setRequestLocale } from "next-intl/server";
import type { Locale } from "@/lib/i18n/routing";

export async function generateMetadata({
	params,
}: {
	params: { lang: Locale };
}) {
	const { lang } = await params;
	const t = await getTranslations({
		locale: lang,
		namespace: "forms.register",
	});
	const common = await getTranslations({ locale: lang, namespace: "common" });

	return {
		title: `${t("title")} - ${common("appName")}`,
		description:
			"Create an account to adopt cats or list cats for adoption",
	};
}

export default async function RegisterPage({
	params,
	searchParams,
}: {
	params: Promise<{ lang: Locale }>;
	searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}) {
	const { lang } = await params;
	const resolvedParams = await searchParams;
	const role = (resolvedParams.role as string) || undefined;

	// Enable static rendering
	setRequestLocale(lang);

	// Get translations
	const t = await getTranslations({
		locale: lang,
		namespace: "forms.register",
	});
	const loginT = await getTranslations({
		locale: lang,
		namespace: "forms.login",
	});

	return (
		<div className="flex min-h-[calc(100vh-4rem)] w-full items-center justify-center">
			<main className="container w-full max-w-md px-4 py-16">
				<Card>
					<CardHeader className="space-y-1">
						<CardTitle className="text-2xl">{t("title")}</CardTitle>
						<CardDescription>
							{t("description") ||
								"Join our community to find or list cats for adoption"}
						</CardDescription>
					</CardHeader>
					<CardContent className="pt-4">
						<RegisterForm initialRole={role} />
					</CardContent>
					<CardFooter className="flex flex-col items-center justify-center gap-2">
						<div className="text-sm text-muted-foreground">
							{t("hasAccount")}{" "}
							<Link
								href={`/${lang}/auth/login`}
								className="text-primary hover:underline"
							>
								{loginT("title")}
							</Link>
						</div>
					</CardFooter>
				</Card>
			</main>
		</div>
	);
}
