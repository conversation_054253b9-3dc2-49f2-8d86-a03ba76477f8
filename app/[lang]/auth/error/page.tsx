import Link from "next/link";
import { Button } from "@/components/ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	Card<PERSON>ooter,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";

export const metadata = {
	title: "Authentication Error - Paws & Whiskers",
	description: "There was a problem with authentication",
};

export default function AuthErrorPage({
	searchParams,
}: {
	searchParams: { [key: string]: string | string[] | undefined };
}) {
	const error = searchParams.error as string | undefined;
	const errorMessage = getErrorMessage(error);

	return (
		<div className="flex min-h-[calc(100vh-4rem)] w-full items-center justify-center">
			<main className="container w-full max-w-md px-4 py-16">
				<Card>
					<CardHeader className="space-y-1">
						<CardTitle className="text-2xl">
							Authentication Error
						</CardTitle>
						<CardDescription>
							There was a problem with your authentication
						</CardDescription>
					</CardHeader>
					<CardContent className="pt-4">
						<div className="text-center space-y-4">
							<div className="text-destructive">
								{errorMessage}
							</div>
						</div>
					</CardContent>
					<CardFooter className="flex flex-col items-center justify-center gap-2">
						<div className="flex space-x-4">
							<Button asChild variant="outline">
								<Link href="/auth/login">Try Again</Link>
							</Button>
							<Button asChild>
								<Link href="/">Go Home</Link>
							</Button>
						</div>
					</CardFooter>
				</Card>
			</main>
		</div>
	);
}

function getErrorMessage(error?: string): string {
	switch (error) {
		case "OAuthAccountNotLinked":
			return "This email is already associated with another account. Please sign in using the original provider.";
		case "EmailSignin":
			return "There was a problem sending the login email. Please try again.";
		case "CredentialsSignin":
			return "Invalid email or password. Please check your credentials and try again.";
		case "SessionRequired":
			return "You need to be signed in to access this page.";
		case "AccessDenied":
			return "You do not have permission to access this resource.";
		case "Verification":
			return "The verification link is invalid or has expired. Please try again.";
		default:
			return "An unknown error occurred. Please try again later.";
	}
}
