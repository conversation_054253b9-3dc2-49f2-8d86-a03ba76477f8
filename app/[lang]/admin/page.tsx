import { redirect } from "next/navigation";
import { AdminDashboard } from "@/components/admin/dashboard";
import { getTranslations } from "next-intl/server";
import { setRequestLocale } from "next-intl/server";
import type { Locale } from "@/lib/i18n/routing";
import { auth } from "@/lib/auth/auth";
import { headers } from "next/headers";
import { db } from "@/lib/db";
import { users } from "@/lib/db/schema";
import { eq } from "drizzle-orm";

export async function generateMetadata({
	params,
}: {
	params: Promise<{ lang: Locale }>;
}) {
	const { lang } = await params;
	const t = await getTranslations({ locale: lang, namespace: "admin" });
	const common = await getTranslations({ locale: lang, namespace: "common" });

	return {
		title: `${t("title") || "Admin Dashboard"} - ${common("appName")}`,
		description: t("description") || "Manage users, cats, and site content",
	};
}

// Get current user session and check admin role
async function getCurrentUser() {
	const session = await auth.api.getSession({
		headers: await headers(),
	});

	if (!session?.user) {
		return null;
	}

	// Get user with role from database
	const user = await db.query.users.findFirst({
		where: eq(users.id, Number(session.user.id)),
		columns: {
			id: true,
			name: true,
			email: true,
			role: true,
		},
	});

	return user;
}

export default async function AdminPage({
	params,
}: {
	params: Promise<{ lang: Locale }>;
}) {
	const { lang } = await params;

	// Enable static rendering
	setRequestLocale(lang);

	// Get translations
	const t = await getTranslations("admin");

	const user = await getCurrentUser();

	// Redirect if not authenticated
	if (!user) {
		redirect(`/${lang}/auth/login?callbackUrl=/${lang}/admin`);
	}

	// Redirect if not admin
	if (user.role !== "admin") {
		redirect(`/${lang}`); // Redirect to home page for non-admin users
	}

	return (
		<div className="w-full p-6">
			<div className="flex items-center justify-between mb-6">
				<h1 className="text-3xl font-bold">
					{t("title") || "Admin Dashboard"}
				</h1>
			</div>
			<AdminDashboard />
		</div>
	);
}
