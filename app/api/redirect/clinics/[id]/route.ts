import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { clinicProfiles } from "@/lib/db/schema";
import { eq } from "drizzle-orm";
import { logSlowQuery } from "@/lib/trpc/routers/helpers/cat-helpers";

export async function GET(
	request: NextRequest,
	{ params }: { params: Promise<{ id: string }> }
) {
	try {
		const { id } = await params;
		const { searchParams } = new URL(request.url);
		const locale = searchParams.get("locale") || "en";

		// Validate ID is numeric
		const clinicId = parseInt(id);
		if (isNaN(clinicId)) {
			console.log(`[API] Invalid clinic ID: ${id}`);
			return NextResponse.redirect(
				new URL(`/${locale}/404`, request.url),
				301
			);
		}

		console.log(`[API] Redirecting clinic ID ${clinicId} to slug-based URL`);

		// Query database for clinic slug
		const startTime = performance.now();
		const clinic = await db.query.clinicProfiles.findFirst({
			where: eq(clinicProfiles.id, clinicId),
			columns: { slug: true, status: true },
		});
		const duration = performance.now() - startTime;
		logSlowQuery("clinicRedirectQuery", duration);

		if (!clinic?.slug) {
			console.log(`[API] No clinic found with ID ${clinicId}`);
			return NextResponse.redirect(
				new URL(`/${locale}/404`, request.url),
				301
			);
		}

		// Only redirect to approved clinics
		if (clinic.status !== "approved") {
			console.log(`[API] Clinic ID ${clinicId} is not approved (status: ${clinic.status})`);
			return NextResponse.redirect(
				new URL(`/${locale}/404`, request.url),
				301
			);
		}

		const newPath = `/${locale}/clinics/${clinic.slug}`;

		// Preserve any additional query parameters (excluding our internal ones)
		const preservedParams = new URLSearchParams();
		for (const [key, value] of searchParams.entries()) {
			if (key !== "locale") {
				preservedParams.append(key, value);
			}
		}

		const finalUrl = preservedParams.toString()
			? `${newPath}?${preservedParams.toString()}`
			: newPath;

		console.log(`[API] Redirecting clinic ID ${clinicId} to: ${finalUrl}`);

		return NextResponse.redirect(new URL(finalUrl, request.url), 301);
	} catch (error) {
		console.error(`[API] Error redirecting clinic:`, error);
		const { searchParams } = new URL(request.url);
		const locale = searchParams.get("locale") || "en";
		return NextResponse.redirect(
			new URL(`/${locale}/404`, request.url),
			301
		);
	}
}
