import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { clinicProfiles, users } from "@/lib/db/schema";
import { eq } from "drizzle-orm";
import { logSlowQuery } from "@/lib/trpc/routers/helpers/cat-helpers";

export async function GET(
	request: NextRequest,
	{ params }: { params: Promise<{ userSlug: string }> }
) {
	try {
		const { userSlug } = await params;
		const { searchParams } = new URL(request.url);
		const locale = searchParams.get("locale") || "en";

		// URL decode the user slug to handle Arabic and other special characters
		const decodedUserSlug = decodeURIComponent(userSlug);

		console.log(`[API] Redirecting legacy clinic user slug ${decodedUserSlug} to new clinic slug-based URL`);

		// Query database for clinic by user slug
		const startTime = performance.now();
		const clinic = await db.query.clinicProfiles.findFirst({
			where: eq(users.slug, decodedUserSlug),
			with: {
				user: {
					columns: {
						slug: true,
					},
				},
			},
			columns: { 
				slug: true, 
				status: true 
			},
		});
		const duration = performance.now() - startTime;
		logSlowQuery("clinicUserSlugRedirectQuery", duration);

		if (!clinic?.slug) {
			console.log(`[API] No clinic found for user slug ${decodedUserSlug}`);
			return NextResponse.redirect(
				new URL(`/${locale}/404`, request.url),
				301
			);
		}

		// Only redirect to approved clinics
		if (clinic.status !== "approved") {
			console.log(`[API] Clinic for user slug ${decodedUserSlug} is not approved (status: ${clinic.status})`);
			return NextResponse.redirect(
				new URL(`/${locale}/404`, request.url),
				301
			);
		}

		const newPath = `/${locale}/clinics/${clinic.slug}`;

		// Preserve any additional query parameters (excluding our internal ones)
		const preservedParams = new URLSearchParams();
		for (const [key, value] of searchParams.entries()) {
			if (key !== "locale") {
				preservedParams.append(key, value);
			}
		}

		const finalUrl = preservedParams.toString()
			? `${newPath}?${preservedParams.toString()}`
			: newPath;

		console.log(`[API] Redirecting legacy clinic user slug ${decodedUserSlug} to: ${finalUrl}`);

		return NextResponse.redirect(new URL(finalUrl, request.url), 301);
	} catch (error) {
		console.error(`[API] Error redirecting clinic by user slug:`, error);
		const { searchParams } = new URL(request.url);
		const locale = searchParams.get("locale") || "en";
		return NextResponse.redirect(
			new URL(`/${locale}/404`, request.url),
			301
		);
	}
}
