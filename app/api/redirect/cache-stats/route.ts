import { NextRequest, NextResponse } from 'next/server';
import { getAllCacheStats } from '@/lib/utils/redirect-cache';

/**
 * API endpoint to get cache statistics for redirect caches
 * Useful for monitoring and debugging cache performance
 */
export async function GET(request: NextRequest) {
	try {
		const stats = getAllCacheStats();
		
		return NextResponse.json({
			success: true,
			timestamp: new Date().toISOString(),
			caches: stats
		});
	} catch (error) {
		console.error('[API] Error getting cache stats:', error);
		
		return NextResponse.json(
			{
				success: false,
				error: 'Failed to get cache statistics'
			},
			{ status: 500 }
		);
	}
}

/**
 * Clear all caches (useful for debugging or manual cache invalidation)
 */
export async function DELETE(request: NextRequest) {
	try {
		const { catRedirectCache, userRedirectCache } = await import('@/lib/utils/redirect-cache');
		
		const beforeStats = getAllCacheStats();
		
		catRedirectCache.clear();
		userRedirectCache.clear();
		
		const afterStats = getAllCacheStats();
		
		return NextResponse.json({
			success: true,
			message: 'All caches cleared',
			before: beforeStats,
			after: afterStats
		});
	} catch (error) {
		console.error('[API] Error clearing caches:', error);
		
		return NextResponse.json(
			{
				success: false,
				error: 'Failed to clear caches'
			},
			{ status: 500 }
		);
	}
}
