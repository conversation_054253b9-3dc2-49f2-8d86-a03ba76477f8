import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth/auth";
import { writeFile } from "fs/promises";
import { join } from "path";
import { v4 as uuidv4 } from "uuid";
import { db } from "@/lib/db";
import { users } from "@/lib/db/schema";
import { eq } from "drizzle-orm";

export async function POST(request: NextRequest) {
	try {
		// Check authentication
		const session = await auth.api.getSession({ headers: request.headers });
		if (!session) {
			return NextResponse.json(
				{ error: "You must be logged in to upload images" },
				{ status: 401 }
			);
		}

		// Get user with role from database
		const user = await db.query.users.findFirst({
			where: eq(users.id, parseInt(session.user.id)),
		});

		if (!user) {
			return NextResponse.json(
				{ error: "User not found" },
				{ status: 404 }
			);
		}

		// Check if user is authorized (rescuer or clinic)
		if (
			user.role !== "rescuer" &&
			user.role !== "clinic" &&
			user.role !== "admin"
		) {
			return NextResponse.json(
				{ error: "You don't have permission to upload images" },
				{ status: 403 }
			);
		}

		// Get the form data
		const formData = await request.formData();
		const file = formData.get("file") as File;

		if (!file) {
			return NextResponse.json(
				{ error: "No file provided" },
				{ status: 400 }
			);
		}

		// Validate file type
		const allowedTypes = [
			"image/jpeg",
			"image/png",
			"image/jpg",
			"image/webp",
		];
		if (!allowedTypes.includes(file.type)) {
			return NextResponse.json(
				{
					error: "File type not allowed. Please upload a JPG, PNG, or WebP image",
				},
				{ status: 400 }
			);
		}

		// Validate file size (5MB max)
		const maxSize = 5 * 1024 * 1024; // 5MB
		if (file.size > maxSize) {
			return NextResponse.json(
				{ error: "File too large. Maximum size is 5MB" },
				{ status: 400 }
			);
		}

		// Create a unique filename
		const fileExtension = file.name.split(".").pop();
		const fileName = `${uuidv4()}.${fileExtension}`;

		// Create the path for the file
		const publicDir = join(process.cwd(), "public");
		const uploadsDir = join(publicDir, "uploads");
		const filePath = join(uploadsDir, fileName);

		// Convert the file to a buffer
		const bytes = await file.arrayBuffer();
		const buffer = Buffer.from(bytes);

		// Write the file to the server
		await writeFile(filePath, buffer);

		// Return the URL of the uploaded file
		const fileUrl = `/uploads/${fileName}`;

		return NextResponse.json({ url: fileUrl });
	} catch (error) {
		console.error("Error uploading file:", error);
		return NextResponse.json(
			{ error: "Failed to upload file" },
			{ status: 500 }
		);
	}
}
