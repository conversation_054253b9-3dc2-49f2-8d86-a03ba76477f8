import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import { match as matchLocale } from "@formatjs/intl-localematcher";
import Negotiator from "negotiator";
import createMiddleware from "next-intl/middleware";
import { locales, defaultLocale } from "./lib/i18n/routing";

// Get the preferred locale from request headers
function getLocale(request: NextRequest): string {
	// Negotiator expects plain object so we need to transform headers
	const negotiatorHeaders: Record<string, string> = {};
	request.headers.forEach((value, key) => (negotiatorHeaders[key] = value));

	// Use negotiator and intl-localematcher to get best locale
	let languages = new Negotiator({ headers: negotiatorHeaders }).languages();

	// @ts-ignore locales are readonly
	return matchLocale(languages, locales, defaultLocale);
}

export default function middleware(request: NextRequest) {
	const pathname = request.nextUrl.pathname;

	// Skip for API routes, Next.js internals, and static files
	if (
		pathname.startsWith("/api/") ||
		pathname.startsWith("/_next/") ||
		pathname.includes("/static/") ||
		pathname.includes(".") // Files with extensions like favicon.ico
	) {
		return NextResponse.next();
	}

	// Create the next-intl middleware
	const handleI18nRouting = createMiddleware({
		// A list of all locales that are supported
		locales,

		// The default locale to use when visiting a non-localized route
		defaultLocale,

		// Always show the locale in the URL
		localePrefix: "always",
	});

	// Check if the pathname already has a locale
	const pathnameHasLocale = locales.some(
		(locale) =>
			pathname.startsWith(`/${locale}/`) || pathname === `/${locale}`
	);

	// If it does, use the next-intl middleware
	if (pathnameHasLocale) return handleI18nRouting(request);

	// Special case for root path
	if (pathname === "/") {
		const locale = getLocale(request);
		return NextResponse.redirect(new URL(`/${locale}`, request.url));
	}

	// Redirect to add the locale prefix
	const locale = getLocale(request);
	return NextResponse.redirect(new URL(`/${locale}${pathname}`, request.url));
}

// Using the new Next.js middleware config format
export const config = {
	// Skip all paths that should not be internationalized
	matcher: ["/((?!api|_next|_vercel|.*\\..*).*)", "/"],
};
