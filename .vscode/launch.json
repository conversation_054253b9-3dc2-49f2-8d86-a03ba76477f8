{
	// Use IntelliSense to learn about possible attributes.
	// Hover to view descriptions of existing attributes.
	// For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
	"version": "0.2.0",
	"configurations": [
		{
			"name": "Next.js: start server",
			"type": "node",
			"request": "launch",
			"runtimeExecutable": "bun",
			"runtimeArgs": ["run", "dev"],
			"skipFiles": ["<node_internals>/**"],
			"console": "integratedTerminal"
		},
		{
			"name": "Firefox: launch browser",
			"type": "firefox",
			"request": "launch",
			"reAttach": true,
			"url": "http://localhost:3000",
			"webRoot": "${workspaceFolder}"
		},
		{
			"name": "Next.js: debug full stack",
			"type": "node",
			"request": "launch",
			"runtimeExecutable": "bun",
			"runtimeArgs": ["run", "dev"],
			"skipFiles": ["<node_internals>/**"],
			"console": "integratedTerminal",
			"serverReadyAction": {
				"pattern": "- Local:.+(https?://.+)",
				"action": "openExternally"
			},
			"sourceMapPathOverrides": {
				"turbopack/[project]/*": "${webRoot}/*"
			}
		}
	],
	"compounds": [
		{
			"name": "Full Stack: Server + Firefox",
			"configurations": [
				"Next.js: start server",
				"Firefox: launch browser"
			]
		}
	]
}
