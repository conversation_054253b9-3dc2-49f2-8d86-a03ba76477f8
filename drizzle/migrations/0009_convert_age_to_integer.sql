-- Migration to convert age column from text to integer
-- This migration safely converts existing age data from text format (e.g., "2 years") to integer (e.g., 2)

-- Step 1: Add a new temporary integer column for age
ALTER TABLE "cats" ADD COLUMN "age_temp" integer;

-- Step 2: Convert existing text age data to integers
-- Extract numeric value from text like "2 years" -> 2, "1 year" -> 1
UPDATE "cats" 
SET "age_temp" = CASE 
    -- Handle "X years" format
    WHEN "age" ~ '^[0-9]+ years?$' THEN 
        CAST(regexp_replace("age", ' years?$', '') AS integer)
    -- Handle "X months" format (convert to years, minimum 1)
    WHEN "age" ~ '^[0-9]+ months?$' THEN 
        GREATEST(1, CAST(regexp_replace("age", ' months?$', '') AS integer) / 12)
    -- Handle pure numeric strings
    WHEN "age" ~ '^[0-9]+$' THEN 
        CAST("age" AS integer)
    -- Handle edge cases - default to 1 year for any unrecognized format
    ELSE 1
END;

-- Step 3: Verify all rows have been converted (should return 0)
-- If this returns any rows, the migration needs to be reviewed
DO $$
DECLARE
    null_count integer;
BEGIN
    SELECT COUNT(*) INTO null_count FROM "cats" WHERE "age_temp" IS NULL;
    IF null_count > 0 THEN
        RAISE EXCEPTION 'Migration failed: % rows have NULL age_temp values', null_count;
    END IF;
END $$;

-- Step 4: Make the new column NOT NULL
ALTER TABLE "cats" ALTER COLUMN "age_temp" SET NOT NULL;

-- Step 5: Drop the old text age column
ALTER TABLE "cats" DROP COLUMN "age";

-- Step 6: Rename the temporary column to age
ALTER TABLE "cats" RENAME COLUMN "age_temp" TO "age";

-- Step 7: Create index on the new numeric age column for performance
CREATE INDEX "cats_age_idx" ON "cats" USING btree ("age");

-- Step 8: Create composite index for age range filtering with draft exclusion
CREATE INDEX "cats_is_draft_age_idx" ON "cats" USING btree ("is_draft", "age");
