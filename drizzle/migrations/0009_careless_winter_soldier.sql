ALTER TABLE "cats" ALTER COLUMN "age" SET DATA TYPE integer;--> statement-breakpoint
CREATE INDEX "cat_images_cat_id_idx" ON "cat_images" USING btree ("cat_id");--> statement-breakpoint
CREATE INDEX "cat_images_is_primary_idx" ON "cat_images" USING btree ("is_primary");--> statement-breakpoint
CREATE INDEX "cat_images_cat_primary_idx" ON "cat_images" USING btree ("cat_id","is_primary");--> statement-breakpoint
CREATE INDEX "cat_images_created_at_idx" ON "cat_images" USING btree ("created_at");--> statement-breakpoint
CREATE INDEX "cats_age_idx" ON "cats" USING btree ("age");--> statement-breakpoint
CREATE INDEX "cats_is_draft_age_idx" ON "cats" USING btree ("is_draft","age");--> statement-breakpoint
CREATE INDEX "chat_participants_user_id_idx" ON "chat_participants" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX "chat_participants_chat_id_idx" ON "chat_participants" USING btree ("chat_id");--> statement-breakpoint
CREATE INDEX "chat_participants_user_chat_idx" ON "chat_participants" USING btree ("user_id","chat_id");--> statement-breakpoint
CREATE INDEX "chat_participants_created_at_idx" ON "chat_participants" USING btree ("created_at");--> statement-breakpoint
CREATE INDEX "chats_cat_id_idx" ON "chats" USING btree ("cat_id");--> statement-breakpoint
CREATE INDEX "chats_created_at_idx" ON "chats" USING btree ("created_at");--> statement-breakpoint
CREATE INDEX "messages_chat_id_idx" ON "messages" USING btree ("chat_id");--> statement-breakpoint
CREATE INDEX "messages_user_id_idx" ON "messages" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX "messages_created_at_idx" ON "messages" USING btree ("created_at");--> statement-breakpoint
CREATE INDEX "messages_status_idx" ON "messages" USING btree ("status");--> statement-breakpoint
CREATE INDEX "messages_chat_created_idx" ON "messages" USING btree ("chat_id","created_at");--> statement-breakpoint
CREATE INDEX "messages_chat_user_idx" ON "messages" USING btree ("chat_id","user_id");