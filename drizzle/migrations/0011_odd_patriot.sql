DROP INDEX "idx_clinic_profiles_search";--> statement-breakpoint
ALTER TABLE "clinic_profiles" ADD COLUMN "wilaya_id" integer;--> statement-breakpoint
ALTER TABLE "clinic_profiles" ADD COLUMN "commune_id" integer;--> statement-breakpoint
ALTER TABLE "clinic_profiles" ADD CONSTRAINT "clinic_profiles_wilaya_id_wilayas_id_fk" FOREIGN KEY ("wilaya_id") REFERENCES "public"."wilayas"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "clinic_profiles" ADD CONSTRAINT "clinic_profiles_commune_id_communes_id_fk" FOREIGN KEY ("commune_id") REFERENCES "public"."communes"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "idx_clinic_profiles_wilaya" ON "clinic_profiles" USING btree ("wilaya_id");--> statement-breakpoint
CREATE INDEX "idx_clinic_profiles_commune" ON "clinic_profiles" USING btree ("commune_id");--> statement-breakpoint
CREATE INDEX "idx_clinic_profiles_search" ON "clinic_profiles" USING btree ("name");--> statement-breakpoint
ALTER TABLE "clinic_profiles" DROP COLUMN "city";--> statement-breakpoint
ALTER TABLE "clinic_profiles" DROP COLUMN "state";--> statement-breakpoint
ALTER TABLE "clinic_profiles" DROP COLUMN "zip";