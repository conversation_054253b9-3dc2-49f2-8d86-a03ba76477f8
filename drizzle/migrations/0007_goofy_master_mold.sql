ALTER TABLE "users" ADD COLUMN "wilaya_id" integer;--> statement-breakpoint
ALTER TABLE "users" ADD COLUMN "commune_id" integer;--> statement-breakpoint
ALTER TABLE "users" ADD CONSTRAINT "users_wilaya_id_wilayas_id_fk" FOREIGN KEY ("wilaya_id") REFERENCES "public"."wilayas"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "users" ADD CONSTRAINT "users_commune_id_communes_id_fk" FOREIGN KEY ("commune_id") REFERENCES "public"."communes"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "cats_is_draft_idx" ON "cats" USING btree ("is_draft");--> statement-breakpoint
CREATE INDEX "cats_adopted_idx" ON "cats" USING btree ("adopted");--> statement-breakpoint
CREATE INDEX "cats_created_at_idx" ON "cats" USING btree ("created_at");--> statement-breakpoint
CREATE INDEX "cats_status_idx" ON "cats" USING btree ("status");--> statement-breakpoint
CREATE INDEX "cats_featured_idx" ON "cats" USING btree ("featured");--> statement-breakpoint
CREATE INDEX "cats_gender_idx" ON "cats" USING btree ("gender");--> statement-breakpoint
CREATE INDEX "cats_vaccinated_idx" ON "cats" USING btree ("vaccinated");--> statement-breakpoint
CREATE INDEX "cats_neutered_idx" ON "cats" USING btree ("neutered");--> statement-breakpoint
CREATE INDEX "cats_special_needs_idx" ON "cats" USING btree ("special_needs");--> statement-breakpoint
CREATE INDEX "users_wilaya_id_idx" ON "users" USING btree ("wilaya_id");--> statement-breakpoint
CREATE INDEX "users_commune_id_idx" ON "users" USING btree ("commune_id");