CREATE INDEX "cat_breeds_name_idx" ON "cat_breeds" USING btree ("name");--> statement-breakpoint
CREATE INDEX "cats_name_idx" ON "cats" USING btree ("name");--> statement-breakpoint
CREATE INDEX "cats_description_idx" ON "cats" USING btree ("description");--> statement-breakpoint
CREATE INDEX "cats_story_idx" ON "cats" USING btree ("story");--> statement-breakpoint
CREATE INDEX "cats_special_needs_desc_idx" ON "cats" USING btree ("special_needs_description");--> statement-breakpoint
CREATE INDEX "cats_breed_id_idx" ON "cats" USING btree ("breed_id");--> statement-breakpoint
CREATE INDEX "cats_wilaya_id_idx" ON "cats" USING btree ("wilaya_id");--> statement-breakpoint
CREATE INDEX "cats_commune_id_idx" ON "cats" USING btree ("commune_id");--> statement-breakpoint
CREATE INDEX "cats_user_id_idx" ON "cats" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX "communes_name_idx" ON "communes" USING btree ("name");--> statement-breakpoint
CREATE INDEX "communes_name_ar_idx" ON "communes" USING btree ("name_ar");--> statement-breakpoint
CREATE INDEX "communes_name_fr_idx" ON "communes" USING btree ("name_fr");--> statement-breakpoint
CREATE INDEX "communes_wilaya_id_idx" ON "communes" USING btree ("wilaya_id");--> statement-breakpoint
CREATE INDEX "users_name_idx" ON "users" USING btree ("name");--> statement-breakpoint
CREATE INDEX "wilayas_name_idx" ON "wilayas" USING btree ("name");--> statement-breakpoint
CREATE INDEX "wilayas_name_ar_idx" ON "wilayas" USING btree ("name_ar");--> statement-breakpoint
CREATE INDEX "wilayas_name_fr_idx" ON "wilayas" USING btree ("name_fr");