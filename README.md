# Paws & Whiskers Cat Adoption Platform

A modern web application for cat adoption built with Next.js 15, React 19, TypeScript, Tailwind CSS, and PostgreSQL.

## Database Setup

### Prerequisites

-   PostgreSQL installed locally or accessible via a connection string
-   Node.js 18+ and Bun

### Setting Up the Database

1. Create a PostgreSQL database:

```bash
# Connect to PostgreSQL (on Linux/Ubuntu systems)
sudo -u postgres psql

# Create the database
CREATE DATABASE cat_adoption_db;

# Create a user for the application (optional but recommended)
CREATE USER cat_user WITH PASSWORD 'your_password';

# Grant privileges to the user
GRANT ALL PRIVILEGES ON DATABASE cat_adoption_db TO cat_user;

# Exit psql
\q
```

2. Create a `.env` file in the project root with the following content:

```
# Database (update with your credentials if you created a custom user)
DATABASE_URL="postgresql://postgres:postgres@localhost:5432/cat_adoption_db"
# If using custom user: DATABASE_URL="postgresql://cat_user:your_password@localhost:5432/cat_adoption_db"

# Auth
NEXTAUTH_SECRET="your-secret-key"
NEXTAUTH_URL="http://localhost:3000"

# API Keys (replace with actual keys when deploying)
NEXT_PUBLIC_API_URL="http://localhost:3000/api"
```

3. Install dependencies:

```bash
bun install
```

4. Generate database migrations:

```bash
bun db:generate
```

5. Apply migrations to the database:

```bash
bun db:migrate
```

6. Seed the database with initial data:

```bash
bun db:seed
```

7. (Optional) Launch Drizzle Studio to view and manage your data:

```bash
bun db:studio
```

## Development

Run the development server:

```bash
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

## Default Users

After seeding, you can log in with the following credentials:

-   Admin:

    -   Email: <EMAIL>
    -   Password: admin123

-   Rescuer:

    -   Email: <EMAIL>
    -   Password: rescuer123

-   Clinic:
    -   Email: <EMAIL>
    -   Password: clinic123

## Authentication

The application uses better-auth for authentication and authorization:

-   **Email/Password Authentication**: Users can register and login using their email and password
-   **OAuth Providers**: Support for Google and GitHub authentication
-   **Role-based Authorization**: Different user roles (admin, adopter, rescuer, clinic) with appropriate permissions
-   **Protected Routes**: Middleware to protect routes based on authentication and role requirements
-   **Custom Login/Register Pages**: User-friendly authentication UI
