import { createAuthClient } from "better-auth/react";
import { adminClient } from "better-auth/client/plugins";

// Create the auth client instance
export const authClient = createAuthClient({
	// Base URL is not needed if the auth server is on the same domain
	plugins: [adminClient()],
});

// Export specific methods for convenience
export const { signIn, signUp, signOut, useSession, getSession } = authClient;
