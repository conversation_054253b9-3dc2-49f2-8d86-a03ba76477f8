import { getRequestConfig } from "next-intl/server";
import { locales, defaultLocale } from "./routing";
import { i18nConfig } from "./config";

export default getRequestConfig(async ({ requestLocale }) => {
	// This typically corresponds to the [locale] segment
	let locale = await requestLocale;

	// Ensure that the incoming locale is valid
	if (!locale || !locales.includes(locale as any)) {
		locale = defaultLocale;
	}

	// Import the messages for the requested locale
	const messages = (await import(`./dictionaries/${locale}.json`)).default;

	return {
		timeZone: i18nConfig.timeZone,
		locale,
		messages,
		// You can add additional configuration here
		// timeZone: 'Europe/Vienna'
	};
});
