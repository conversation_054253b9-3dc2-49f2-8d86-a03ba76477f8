"use client";

import { NextIntlClientProvider } from "next-intl";
import { ReactNode } from "react";
import { i18nConfig } from "./config";

export function NextIntlProvider({
	locale,
	messages,
	children,
}: {
	locale: string;
	messages: any;
	children: ReactNode;
}) {
	return (
		<NextIntlClientProvider
			locale={locale}
			messages={messages}
			timeZone={i18nConfig.timeZone}
		>
			{children}
		</NextIntlClientProvider>
	);
}
