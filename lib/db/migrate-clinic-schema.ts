import postgres from "postgres";
import { drizzle } from "drizzle-orm/postgres-js";

// Manual migration script for clinic schema updates
async function main() {
	console.log("Running clinic schema migration...");

	const connectionString =
		process.env.DATABASE_URL ||
		"postgresql://postgres:postgres@localhost:5432/cat_adoption_db";

	// Create connection
	const migrationClient = postgres(connectionString, { max: 1 });

	try {
		// Initialize drizzle with the migration client
		const db = drizzle(migrationClient);

		console.log("Step 1: Converting services column to array...");
		
		// First, handle the services column conversion
		// Convert existing text data to array format
		await migrationClient`
			ALTER TABLE clinic_profiles 
			ALTER COLUMN services 
			SET DATA TYPE text[] 
			USING CASE 
				WHEN services IS NULL THEN NULL
				WHEN services = '' THEN ARRAY[]::text[]
				ELSE string_to_array(services, ',')
			END
		`;

		console.log("Step 2: Adding operating_hours column...");
		
		// Add the operating_hours column
		await migrationClient`
			ALTER TABLE clinic_profiles 
			ADD COLUMN IF NOT EXISTS operating_hours jsonb
		`;

		console.log("Step 3: Adding featured column...");
		
		// Add the featured column
		await migrationClient`
			ALTER TABLE clinic_profiles 
			ADD COLUMN IF NOT EXISTS featured boolean DEFAULT false NOT NULL
		`;

		console.log("Step 4: Creating indexes...");
		
		// Create indexes
		await migrationClient`
			CREATE INDEX IF NOT EXISTS idx_clinic_profiles_services 
			ON clinic_profiles USING GIN(services)
		`;

		await migrationClient`
			CREATE INDEX IF NOT EXISTS idx_clinic_profiles_featured 
			ON clinic_profiles(featured)
		`;

		await migrationClient`
			CREATE INDEX IF NOT EXISTS idx_clinic_profiles_search 
			ON clinic_profiles(name, city, state)
		`;

		console.log("Clinic schema migration completed successfully!");
	} catch (error) {
		console.error("Migration failed:", error);
		process.exit(1);
	} finally {
		// Close connection
		await migrationClient.end();
	}
}

main();
