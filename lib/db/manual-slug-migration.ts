import postgres from "postgres";
import { drizzle } from "drizzle-orm/postgres-js";
import { generateSlug } from "@/lib/utils/slug";

/**
 * Manual migration script to add slug columns safely
 * This handles existing data by:
 * 1. Adding nullable slug columns
 * 2. Populating slugs for existing data
 * 3. Making columns NOT NULL and UNIQUE
 */
async function runSlugMigration() {
	console.log("Starting manual slug migration...");

	const connectionString =
		process.env.DATABASE_URL ||
		"postgresql://postgres:postgres@localhost:5432/cat_adoption_db";

	const client = postgres(connectionString, { max: 1 });
	const db = drizzle(client);

	try {
		// Step 1: Add nullable slug columns
		console.log("Step 1: Adding nullable slug columns...");
		await client`ALTER TABLE "cats" ADD COLUMN IF NOT EXISTS "slug" text`;
		await client`ALTER TABLE "users" ADD COLUMN IF NOT EXISTS "slug" text`;

		// Step 2: Populate slugs for existing cats
		console.log("Step 2: Populating cat slugs...");
		const cats = await client`SELECT id, name, slug FROM cats WHERE slug IS NULL OR slug = ''`;
		console.log(`Found ${cats.length} cats without slugs`);

		const usedCatSlugs = new Set<string>();
		
		for (const cat of cats) {
			let baseSlug = generateSlug(cat.name);
			let slug = baseSlug;
			let counter = 1;

			// Ensure uniqueness
			while (usedCatSlugs.has(slug)) {
				slug = `${baseSlug}-${counter}`;
				counter++;
			}
			usedCatSlugs.add(slug);

			await client`UPDATE cats SET slug = ${slug} WHERE id = ${cat.id}`;
			console.log(`  Cat "${cat.name}" -> "${slug}"`);
		}

		// Step 3: Populate slugs for existing users
		console.log("Step 3: Populating user slugs...");
		const users = await client`SELECT id, name, slug FROM users WHERE slug IS NULL OR slug = ''`;
		console.log(`Found ${users.length} users without slugs`);

		const usedUserSlugs = new Set<string>();
		
		for (const user of users) {
			let baseSlug = generateSlug(user.name);
			let slug = baseSlug;
			let counter = 1;

			// Ensure uniqueness
			while (usedUserSlugs.has(slug)) {
				slug = `${baseSlug}-${counter}`;
				counter++;
			}
			usedUserSlugs.add(slug);

			await client`UPDATE users SET slug = ${slug} WHERE id = ${user.id}`;
			console.log(`  User "${user.name}" -> "${slug}"`);
		}

		// Step 4: Make columns NOT NULL and add constraints
		console.log("Step 4: Adding NOT NULL constraints and indexes...");
		
		// Make columns NOT NULL
		await client`ALTER TABLE "cats" ALTER COLUMN "slug" SET NOT NULL`;
		await client`ALTER TABLE "users" ALTER COLUMN "slug" SET NOT NULL`;

		// Add unique constraints
		await client`ALTER TABLE "cats" ADD CONSTRAINT "cats_slug_unique" UNIQUE("slug")`;
		await client`ALTER TABLE "users" ADD CONSTRAINT "users_slug_unique" UNIQUE("slug")`;

		// Add indexes for performance
		await client`CREATE INDEX IF NOT EXISTS "cats_slug_idx" ON "cats" USING btree ("slug")`;
		await client`CREATE INDEX IF NOT EXISTS "users_slug_idx" ON "users" USING btree ("slug")`;

		console.log("Migration completed successfully!");

	} catch (error) {
		console.error("Migration failed:", error);
		throw error;
	} finally {
		await client.end();
	}
}

// Run the migration if this file is executed directly
if (require.main === module) {
	runSlugMigration()
		.then(() => {
			console.log("Slug migration completed!");
			process.exit(0);
		})
		.catch((error) => {
			console.error("Slug migration failed:", error);
			process.exit(1);
		});
}

export { runSlugMigration };
