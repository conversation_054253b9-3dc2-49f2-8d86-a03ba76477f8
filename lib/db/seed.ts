import { db } from "./index";
import {
	users,
	cats,
	catImages,
	clinicProfiles,
	accounts,
	catBreeds,
} from "./schema";
import { scrypt, randomBytes } from "crypto";
import { promisify } from "util";
import { generateSlug } from "../utils/slug";

// Convert scrypt to promise-based
const scryptAsync = promisify(scrypt);

// Create a password hash using scrypt
async function createHash(password: string) {
	const salt = randomBytes(16).toString("hex");
	const buf = (await scryptAsync(
		password,
		salt + process.env.BETTER_AUTH_SECRET,
		64
	)) as Buffer;
	return `${buf.toString("hex")}.${salt}`;
}

async function seed() {
	console.log("🌱 Seeding database...");

	try {
		console.log("🐱 Seeding cat breeds...");

		const breeds = [
			{
				name: "Domestic Shorthair",
				description: "Most common cat breed with short coat",
				origin: "Various",
				temperament: "Varies, generally friendly and adaptable",
			},
			{
				name: "Domestic Longhair",
				description: "Mixed breed cat with long coat",
				origin: "Various",
				temperament: "Varies, often gentle and affectionate",
			},
			{
				name: "Siamese",
				description: "Distinctive pointed cat with blue eyes",
				origin: "Thailand",
				temperament: "Vocal, intelligent, social, demanding",
			},
			{
				name: "Persian",
				description: "Long-haired breed with round face",
				origin: "Persia (Iran)",
				temperament: "Sweet, gentle, quiet, needs attention",
			},
			{
				name: "Maine Coon",
				description: "Large, muscular cat with long coat",
				origin: "United States",
				temperament:
					"Gentle, friendly, intelligent, good with children",
			},
			{
				name: "Bengal",
				description:
					"Spotted and marbled patterns resembling wild cats",
				origin: "United States",
				temperament: "Active, energetic, playful, intelligent",
			},
			{
				name: "Ragdoll",
				description: "Large, gentle cat with semi-long coat",
				origin: "United States",
				temperament: "Relaxed, gentle, affectionate, easy-going",
			},
			{
				name: "Sphynx",
				description: "Hairless cat with distinctive appearance",
				origin: "Canada",
				temperament: "Friendly, energetic, intelligent, inquisitive",
			},
			{
				name: "British Shorthair",
				description: "Stocky body with dense coat",
				origin: "United Kingdom",
				temperament: "Easygoing, calm, loyal, independent",
			},
			{
				name: "Scottish Fold",
				description: "Known for folded ears due to genetic mutation",
				origin: "Scotland",
				temperament: "Sweet, adaptable, playful, intelligent",
			},
			{
				name: "Mixed Breed",
				description: "Cats of mixed ancestry",
				origin: "Various",
				temperament: "Varies widely",
			},
		];

		for (const breed of breeds) {
			await db.insert(catBreeds).values({
				name: breed.name,
				description: breed.description,
				origin: breed.origin,
				temperament: breed.temperament,
				createdAt: new Date(),
				updatedAt: new Date(),
			});
		}

		console.log("✅ Cat breeds seeded successfully!");

		// Get or create cat breeds
		console.log("🔍 Checking for cat breeds...");
		const breedsData = await db.select().from(catBreeds);
		const breedMap = new Map(
			breedsData.map((breed) => [breed.name, breed.id])
		);

		if (breedsData.length === 0) {
			console.warn(
				"⚠️ No cat breeds found in database. Please run seed-location-breed.ts first."
			);
		}

		// Create admin user
		const adminPasswordHash = await createHash("admin123");
		const [admin] = await db
			.insert(users)
			.values({
				name: "Admin User",
				slug: generateSlug("Admin User"),
				email: "<EMAIL>",
				role: "admin",
				location: "San Francisco, CA",
			})
			.returning();
		console.log("✅ Admin user created");

		// Create account for admin
		await db.insert(accounts).values({
			userId: admin.id,
			providerId: "credentials",
			accountId: admin.email,
			password: adminPasswordHash,
			createdAt: new Date(),
			updatedAt: new Date(),
		});
		console.log("✅ Admin account created");

		// Create rescuer user
		const rescuerPasswordHash = await createHash("rescuer123");
		const [rescuer] = await db
			.insert(users)
			.values({
				name: "Cat Rescuer",
				slug: generateSlug("Cat Rescuer"),
				email: "<EMAIL>",
				role: "rescuer",
				bio: "Dedicated to rescuing cats from the streets",
				location: "Los Angeles, CA",
				phone: "************",
			})
			.returning();
		console.log("✅ Rescuer user created");

		// Create account for rescuer
		await db.insert(accounts).values({
			userId: rescuer.id,
			providerId: "credentials",
			accountId: rescuer.email,
			password: rescuerPasswordHash,
			createdAt: new Date(),
			updatedAt: new Date(),
		});
		console.log("✅ Rescuer account created");

		// Create clinic user
		const clinicPasswordHash = await createHash("clinic123");
		const [clinic] = await db
			.insert(users)
			.values({
				name: "Happy Paws Clinic",
				slug: generateSlug("Happy Paws Clinic"),
				email: "<EMAIL>",
				role: "clinic",
				location: "San Diego, CA",
				phone: "************",
			})
			.returning();
		console.log("✅ Clinic user created");

		// Create account for clinic
		await db.insert(accounts).values({
			userId: clinic.id,
			providerId: "credentials",
			accountId: clinic.email,
			password: clinicPasswordHash,
			createdAt: new Date(),
			updatedAt: new Date(),
		});
		console.log("✅ Clinic account created");

		// Create clinic profile
		await db.insert(clinicProfiles).values({
			userId: clinic.id,
			name: "Happy Paws Veterinary Clinic",
			address: "123 Main Street",
			city: "San Diego",
			state: "CA",
			zip: "92101",
			phone: "************",
			website: "https://happypaws.example.com",
			services: "Vaccinations, Spay/Neuter, Checkups, Emergency Care",
		});
		console.log("✅ Clinic profile created");

		// Create sample cats
		const [cat1] = await db
			.insert(cats)
			.values({
				name: "Whiskers",
				slug: generateSlug("Whiskers"),
				gender: "male",
				age: 2, // Changed to numeric value
				breedId: breedMap.get("Domestic Shorthair"),
				description: "Friendly and playful cat who loves attention",
				story: "Whiskers was found as a stray in a local park. He was hungry but friendly.",
				wilayaId: null,
				communeId: null,
				vaccinated: true,
				neutered: true,
				specialNeeds: false,
				featured: true,
				userId: rescuer.id,
			})
			.returning();
		console.log("✅ Cat 1 created");

		const [cat2] = await db
			.insert(cats)
			.values({
				name: "Luna",
				slug: generateSlug("Luna"),
				gender: "female",
				age: 1, // Changed to numeric value
				breedId: breedMap.get("Siamese") || breedMap.get("Mixed Breed"),
				description:
					"Quiet and gentle cat who enjoys lounging in sunny spots",
				story: "Luna was surrendered by a family who could no longer care for her due to allergies.",
				wilayaId: null,
				communeId: null,
				vaccinated: true,
				neutered: true,
				specialNeeds: false,
				featured: true,
				userId: rescuer.id,
			})
			.returning();
		console.log("✅ Cat 2 created");

		const [cat3] = await db
			.insert(cats)
			.values({
				name: "Oliver",
				slug: generateSlug("Oliver"),
				gender: "male",
				age: 3, // Changed to numeric value
				breedId:
					breedMap.get("Domestic Shorthair") ||
					breedMap.get("Mixed Breed"),
				description: "Energetic and curious cat who loves to explore",
				story: "Oliver was rescued from a hoarding situation along with 20 other cats.",
				wilayaId: null,
				communeId: null,
				vaccinated: true,
				neutered: false,
				specialNeeds: true,
				featured: false,
				userId: rescuer.id,
			})
			.returning();
		console.log("✅ Cat 3 created");

		// Add cat images
		await db.insert(catImages).values([
			{
				catId: cat1.id,
				url: "/images/cats/whiskers-1.jpg",
				isPrimary: true,
			},
			{
				catId: cat1.id,
				url: "/images/cats/whiskers-2.jpg",
				isPrimary: false,
			},
			{
				catId: cat2.id,
				url: "/images/cats/luna-1.jpg",
				isPrimary: true,
			},
			{
				catId: cat3.id,
				url: "/images/cats/oliver-1.jpg",
				isPrimary: true,
			},
		]);
		console.log("✅ Cat images created");

		console.log("✅ Seeding completed successfully!");
	} catch (error) {
		console.error("❌ Seeding failed:", error);
		process.exit(1);
	}
}

// Run the seed function
seed()
	.then(() => process.exit(0))
	.catch((error) => {
		console.error("Unhandled error during seeding:", error);
		process.exit(1);
	});
