import { db } from "@/lib/db";
import { cats, users } from "@/lib/db/schema";
import { generateUniqueCatSlug, generateUniqueUserSlug } from "@/lib/utils/slug";
import { eq } from "drizzle-orm";

/**
 * Migration script to add slugs to existing cats and users
 * This should be run after the database schema has been updated
 */
async function migrateExistingData() {
	console.log("Starting slug migration for existing data...");

	try {
		// First, let's check if the slug columns exist
		console.log("Checking database schema...");

		// Get all cats without slugs
		const catsWithoutSlugs = await db.query.cats.findMany({
			where: (cats, { isNull, or, eq }) => or(isNull(cats.slug), eq(cats.slug, "")),
		});

		console.log(`Found ${catsWithoutSlugs.length} cats without slugs`);

		// Generate slugs for cats
		for (const cat of catsWithoutSlugs) {
			console.log(`Generating slug for cat: ${cat.name} (ID: ${cat.id})`);
			const slug = await generateUniqueCatSlug(cat.name, cat.id);
			
			await db
				.update(cats)
				.set({ slug })
				.where(eq(cats.id, cat.id));
			
			console.log(`  Generated slug: ${slug}`);
		}

		// Get all users without slugs
		const usersWithoutSlugs = await db.query.users.findMany({
			where: (users, { isNull, or, eq }) => or(isNull(users.slug), eq(users.slug, "")),
		});

		console.log(`Found ${usersWithoutSlugs.length} users without slugs`);

		// Generate slugs for users
		for (const user of usersWithoutSlugs) {
			console.log(`Generating slug for user: ${user.name} (ID: ${user.id})`);
			const slug = await generateUniqueUserSlug(user.name, user.id);
			
			await db
				.update(users)
				.set({ slug })
				.where(eq(users.id, user.id));
			
			console.log(`  Generated slug: ${slug}`);
		}

		console.log("Slug migration completed successfully!");

	} catch (error) {
		console.error("Migration failed:", error);
		process.exit(1);
	}
}

// Run the migration if this file is executed directly
if (require.main === module) {
	migrateExistingData()
		.then(() => {
			console.log("Migration completed!");
			process.exit(0);
		})
		.catch((error) => {
			console.error("Migration failed:", error);
			process.exit(1);
		});
}

export { migrateExistingData };
