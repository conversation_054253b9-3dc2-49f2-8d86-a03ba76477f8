import { db } from "@/lib/db";
import { clinicProfiles } from "@/lib/db/schema";
import { generateUniqueClinicSlug } from "@/lib/utils/slug";
import { eq, isNull, or } from "drizzle-orm";

/**
 * Migration script to add slug field to clinic_profiles table and generate slugs for existing clinics
 * This should be run after the database schema has been updated to include the slug field
 */
export async function migrateClinicSlugs() {
	console.log("🏥 Starting clinic slug migration...");

	try {
		// First, add the slug column to the database if it doesn't exist
		console.log("Step 1: Adding slug column to clinic_profiles table...");
		
		await db.execute(`
			ALTER TABLE clinic_profiles 
			ADD COLUMN IF NOT EXISTS slug text
		`);

		console.log("Step 2: Fetching clinics without slugs...");

		// Get all clinics without slugs
		const clinicsWithoutSlugs = await db.query.clinicProfiles.findMany({
			where: or(isNull(clinicProfiles.slug), eq(clinicProfiles.slug, "")),
			columns: {
				id: true,
				name: true,
				slug: true,
			},
		});

		console.log(`Found ${clinicsWithoutSlugs.length} clinics without slugs`);

		if (clinicsWithoutSlugs.length === 0) {
			console.log("✅ No clinics need slug generation. Migration complete!");
			return;
		}

		// Generate slugs for clinics
		console.log("Step 3: Generating slugs for clinics...");
		
		for (const clinic of clinicsWithoutSlugs) {
			console.log(`Generating slug for clinic: ${clinic.name} (ID: ${clinic.id})`);
			
			try {
				const slug = await generateUniqueClinicSlug(clinic.name, clinic.id);
				
				await db
					.update(clinicProfiles)
					.set({ slug })
					.where(eq(clinicProfiles.id, clinic.id));
				
				console.log(`  ✅ Generated slug: ${slug}`);
			} catch (error) {
				console.error(`  ❌ Failed to generate slug for clinic ${clinic.id}:`, error);
				// Continue with other clinics even if one fails
			}
		}

		console.log("Step 4: Adding unique constraint and index to slug column...");
		
		// Add unique constraint to slug column
		await db.execute(`
			ALTER TABLE clinic_profiles 
			ADD CONSTRAINT clinic_profiles_slug_unique UNIQUE (slug)
		`);

		// Add index for slug lookups (if not already exists from schema)
		await db.execute(`
			CREATE INDEX IF NOT EXISTS idx_clinic_profiles_slug 
			ON clinic_profiles(slug)
		`);

		console.log("Step 5: Making slug column NOT NULL...");
		
		// Make slug column NOT NULL
		await db.execute(`
			ALTER TABLE clinic_profiles 
			ALTER COLUMN slug SET NOT NULL
		`);

		console.log("✅ Clinic slug migration completed successfully!");

		// Verify the migration
		console.log("Step 6: Verifying migration...");
		
		const clinicsWithSlugs = await db.query.clinicProfiles.findMany({
			columns: {
				id: true,
				name: true,
				slug: true,
			},
		});

		const clinicsWithoutSlugsAfter = clinicsWithSlugs.filter(
			clinic => !clinic.slug || clinic.slug.trim() === ""
		);

		if (clinicsWithoutSlugsAfter.length > 0) {
			console.warn(`⚠️  Warning: ${clinicsWithoutSlugsAfter.length} clinics still don't have slugs:`);
			clinicsWithoutSlugsAfter.forEach(clinic => {
				console.warn(`  - ${clinic.name} (ID: ${clinic.id})`);
			});
		} else {
			console.log("✅ All clinics now have slugs!");
		}

		console.log(`📊 Migration summary:`);
		console.log(`  - Total clinics processed: ${clinicsWithoutSlugs.length}`);
		console.log(`  - Clinics with slugs after migration: ${clinicsWithSlugs.length}`);
		console.log(`  - Clinics still missing slugs: ${clinicsWithoutSlugsAfter.length}`);

	} catch (error) {
		console.error("❌ Clinic slug migration failed:", error);
		throw error;
	}
}

/**
 * Rollback function to remove slug column (use with caution)
 */
export async function rollbackClinicSlugMigration() {
	console.log("🔄 Rolling back clinic slug migration...");
	
	try {
		// Remove unique constraint
		await db.execute(`
			ALTER TABLE clinic_profiles 
			DROP CONSTRAINT IF EXISTS clinic_profiles_slug_unique
		`);

		// Remove index
		await db.execute(`
			DROP INDEX IF EXISTS idx_clinic_profiles_slug
		`);

		// Remove slug column
		await db.execute(`
			ALTER TABLE clinic_profiles 
			DROP COLUMN IF EXISTS slug
		`);

		console.log("✅ Clinic slug migration rollback completed!");
	} catch (error) {
		console.error("❌ Rollback failed:", error);
		throw error;
	}
}

// If this script is run directly
if (require.main === module) {
	migrateClinicSlugs()
		.then(() => {
			console.log("Migration completed successfully!");
			process.exit(0);
		})
		.catch((error) => {
			console.error("Migration failed:", error);
			process.exit(1);
		});
}
