-- Add status field to clinic_profiles table for approval workflow
-- Migration: Add clinic status field
-- Date: 2025-07-26

-- Add status column with default 'pending'
ALTER TABLE clinic_profiles 
ADD COLUMN status TEXT NOT NULL DEFAULT 'pending' 
CHECK (status IN ('pending', 'approved', 'rejected'));

-- Add index for status filtering
CREATE INDEX idx_clinic_profiles_status ON clinic_profiles(status);

-- Update existing clinics to 'approved' status (assuming they were already approved)
UPDATE clinic_profiles SET status = 'approved' WHERE status = 'pending';

-- Add comment for documentation
COMMENT ON COLUMN clinic_profiles.status IS 'Clinic application status: pending, approved, rejected';
