import { db } from "../index";
import { clinicProfiles, wilayas, communes } from "../schema";
import { sql } from "drizzle-orm";

/**
 * Migration script to update clinic_profiles table:
 * 1. Add wilayaId and communeId columns
 * 2. Migrate existing city/state data to wilaya/commune references where possible
 * 3. Remove city, state, and zip columns
 * 4. Add new indexes for wilaya and commune filtering
 */

interface ExistingClinicProfile {
	id: number;
	city: string;
	state: string;
	zip: string;
}

export async function migrateClinicLocations() {
	console.log("🏥 Starting clinic location migration...");

	try {
		// Step 1: Add new columns (wilayaId, communeId) if they don't exist
		console.log("📝 Adding wilayaId and communeId columns...");
		
		await db.execute(sql`
			ALTER TABLE clinic_profiles 
			ADD COLUMN IF NOT EXISTS wilaya_id INTEGER REFERENCES wilayas(id),
			ADD COLUMN IF NOT EXISTS commune_id INTEGER REFERENCES communes(id)
		`);

		// Step 2: Fetch existing clinic profiles with location data
		console.log("📊 Fetching existing clinic profiles...");
		
		const existingProfiles = await db.execute(sql`
			SELECT id, city, state, zip 
			FROM clinic_profiles 
			WHERE city IS NOT NULL OR state IS NOT NULL
		`) as ExistingClinicProfile[];

		console.log(`Found ${existingProfiles.length} clinic profiles to migrate`);

		// Step 3: Fetch all wilayas and communes for matching
		const allWilayas = await db.query.wilayas.findMany();
		const allCommunes = await db.query.communes.findMany({
			with: {
				wilaya: true,
			},
		});

		// Step 4: Migrate each clinic profile
		for (const profile of existingProfiles) {
			console.log(`Migrating clinic profile ${profile.id}...`);

			let wilayaId: number | null = null;
			let communeId: number | null = null;

			// Try to match state to wilaya
			if (profile.state) {
				const matchedWilaya = allWilayas.find(
					(w) =>
						w.name.toLowerCase().includes(profile.state.toLowerCase()) ||
						w.nameAr?.includes(profile.state) ||
						w.nameFr?.toLowerCase().includes(profile.state.toLowerCase()) ||
						profile.state.toLowerCase().includes(w.name.toLowerCase())
				);

				if (matchedWilaya) {
					wilayaId = matchedWilaya.id;
					console.log(`  ✅ Matched state "${profile.state}" to wilaya "${matchedWilaya.name}"`);

					// Try to match city to commune within the matched wilaya
					if (profile.city) {
						const matchedCommune = allCommunes.find(
							(c) =>
								c.wilayaId === wilayaId &&
								(c.name.toLowerCase().includes(profile.city.toLowerCase()) ||
									c.nameAr?.includes(profile.city) ||
									c.nameFr?.toLowerCase().includes(profile.city.toLowerCase()) ||
									profile.city.toLowerCase().includes(c.name.toLowerCase()))
						);

						if (matchedCommune) {
							communeId = matchedCommune.id;
							console.log(`  ✅ Matched city "${profile.city}" to commune "${matchedCommune.name}"`);
						} else {
							console.log(`  ⚠️ Could not match city "${profile.city}" to any commune in wilaya "${matchedWilaya.name}"`);
						}
					}
				} else {
					console.log(`  ⚠️ Could not match state "${profile.state}" to any wilaya`);
				}
			}

			// Update the clinic profile with the matched IDs
			if (wilayaId || communeId) {
				await db.execute(sql`
					UPDATE clinic_profiles 
					SET wilaya_id = ${wilayaId}, commune_id = ${communeId}
					WHERE id = ${profile.id}
				`);
				console.log(`  ✅ Updated clinic profile ${profile.id} with wilayaId: ${wilayaId}, communeId: ${communeId}`);
			} else {
				console.log(`  ⚠️ No location match found for clinic profile ${profile.id}`);
			}
		}

		// Step 5: Create indexes for the new columns
		console.log("📊 Creating indexes for wilaya and commune columns...");
		
		await db.execute(sql`
			CREATE INDEX IF NOT EXISTS idx_clinic_profiles_wilaya ON clinic_profiles(wilaya_id)
		`);
		
		await db.execute(sql`
			CREATE INDEX IF NOT EXISTS idx_clinic_profiles_commune ON clinic_profiles(commune_id)
		`);

		// Step 6: Drop old columns (city, state, zip)
		console.log("🗑️ Removing old location columns...");
		
		await db.execute(sql`
			ALTER TABLE clinic_profiles 
			DROP COLUMN IF EXISTS city,
			DROP COLUMN IF EXISTS state,
			DROP COLUMN IF EXISTS zip
		`);

		// Step 7: Update the search index to only include name
		console.log("📊 Updating search index...");
		
		await db.execute(sql`
			DROP INDEX IF EXISTS idx_clinic_profiles_search
		`);
		
		await db.execute(sql`
			CREATE INDEX IF NOT EXISTS idx_clinic_profiles_search ON clinic_profiles(name)
		`);

		console.log("✅ Clinic location migration completed successfully!");

	} catch (error) {
		console.error("❌ Error during clinic location migration:", error);
		throw error;
	}
}

// Run migration if this file is executed directly
if (require.main === module) {
	migrateClinicLocations()
		.then(() => {
			console.log("Migration completed successfully");
			process.exit(0);
		})
		.catch((error) => {
			console.error("Migration failed:", error);
			process.exit(1);
		});
}
