#!/usr/bin/env bun

/**
 * Migration script to implement two-tier service management system
 *
 * This script:
 * 1. Creates the service_types table for admin-managed service definitions
 * 2. Migrates existing clinic_services data to the new structure
 * 3. Updates the clinic_services table schema
 * 4. Creates default service types from existing services
 */

import postgres from "postgres";
import { drizzle } from "drizzle-orm/postgres-js";
import { eq, sql } from "drizzle-orm";
import { serviceTypes, clinicServices, users } from "./schema";

// Database connection
const connectionString = process.env.DATABASE_URL;
if (!connectionString) {
	console.error("DATABASE_URL environment variable is required");
	process.exit(1);
}

async function migrateServiceTypes() {
	const migrationClient = postgres(connectionString, { max: 1 });

	try {
		// Initialize drizzle with the migration client
		const db = drizzle(migrationClient);

		console.log("Step 1: Creating service_types table...");

		// Create the service_types table
		await migrationClient`
			CREATE TABLE IF NOT EXISTS service_types (
				id SERIAL PRIMARY KEY,
				name TEXT NOT NULL UNIQUE,
				description TEXT NOT NULL,
				category TEXT NOT NULL,
				is_active BOOLEAN DEFAULT true NOT NULL,
				requires_appointment BOOLEAN DEFAULT false NOT NULL,
				display_order INTEGER DEFAULT 0 NOT NULL,
				created_at TIMESTAMP DEFAULT NOW() NOT NULL,
				updated_at TIMESTAMP DEFAULT NOW() NOT NULL,
				created_by INTEGER NOT NULL REFERENCES users(id)
			)
		`;

		// Create indexes for service_types (separate statements)
		await migrationClient`CREATE INDEX IF NOT EXISTS idx_service_types_category ON service_types(category)`;
		await migrationClient`CREATE INDEX IF NOT EXISTS idx_service_types_active ON service_types(is_active)`;
		await migrationClient`CREATE INDEX IF NOT EXISTS idx_service_types_order ON service_types(display_order)`;
		await migrationClient`CREATE INDEX IF NOT EXISTS idx_service_types_name ON service_types(name)`;
		await migrationClient`CREATE INDEX IF NOT EXISTS idx_service_types_category_active ON service_types(category, is_active)`;

		console.log("Step 2: Finding admin user for service type creation...");

		// Find the first admin user to assign as creator of default service types
		const adminUser = await migrationClient`
			SELECT id FROM users WHERE role = 'admin' LIMIT 1
		`;

		if (adminUser.length === 0) {
			console.error(
				"No admin user found. Please create an admin user first."
			);
			process.exit(1);
		}

		const adminUserId = adminUser[0].id;
		console.log(`Using admin user ID: ${adminUserId}`);

		console.log(
			"Step 3: Creating default service types from existing services..."
		);

		// Get unique service names and categories from existing clinic_services
		const existingServices = await migrationClient`
			SELECT DISTINCT
				name,
				description,
				category,
				requires_appointment,
				ROW_NUMBER() OVER (ORDER BY name) as display_order
			FROM clinic_services
			WHERE name IS NOT NULL AND description IS NOT NULL
			ORDER BY name
		`;

		console.log(
			`Found ${existingServices.length} unique services to convert to service types`
		);

		// Create service types from existing services
		const defaultServiceTypes = [
			{
				name: "Vaccination",
				description:
					"Complete vaccination services for cats including core and non-core vaccines",
				category: "Preventive Care",
				requiresAppointment: true,
				displayOrder: 1,
			},
			{
				name: "Spay/Neuter",
				description: "Surgical sterilization procedures for cats",
				category: "Surgery",
				requiresAppointment: true,
				displayOrder: 2,
			},
			{
				name: "Health Check-up",
				description:
					"Comprehensive health examination and wellness check",
				category: "Preventive Care",
				requiresAppointment: true,
				displayOrder: 3,
			},
			{
				name: "Emergency Care",
				description: "24/7 emergency veterinary services",
				category: "Emergency",
				requiresAppointment: false,
				displayOrder: 4,
			},
			{
				name: "Dental Care",
				description:
					"Dental cleaning, examination, and treatment services",
				category: "Dental",
				requiresAppointment: true,
				displayOrder: 5,
			},
			{
				name: "Microchipping",
				description: "Pet identification microchip implantation",
				category: "Preventive Care",
				requiresAppointment: true,
				displayOrder: 6,
			},
		];

		// Insert default service types
		for (const serviceType of defaultServiceTypes) {
			await migrationClient`
				INSERT INTO service_types (name, description, category, requires_appointment, display_order, created_by)
				VALUES (${serviceType.name}, ${serviceType.description}, ${serviceType.category}, ${serviceType.requiresAppointment}, ${serviceType.displayOrder}, ${adminUserId})
				ON CONFLICT (name) DO NOTHING
			`;
		}

		// Insert service types from existing unique services
		for (const service of existingServices) {
			const category = service.category || "General";
			await migrationClient`
				INSERT INTO service_types (name, description, category, requires_appointment, display_order, created_by)
				VALUES (${service.name}, ${service.description}, ${category}, ${service.requires_appointment || false}, ${service.display_order + 10}, ${adminUserId})
				ON CONFLICT (name) DO NOTHING
			`;
		}

		console.log("Step 4: Backing up existing clinic_services data...");

		// Create backup table
		await migrationClient`
			CREATE TABLE IF NOT EXISTS clinic_services_backup AS
			SELECT * FROM clinic_services
		`;

		console.log(
			"Step 5: Adding service_type_id column to clinic_services..."
		);

		// Add the new service_type_id column
		await migrationClient`
			ALTER TABLE clinic_services
			ADD COLUMN IF NOT EXISTS service_type_id INTEGER REFERENCES service_types(id) ON DELETE CASCADE
		`;

		console.log("Step 6: Mapping existing services to service types...");

		// Update existing clinic_services to reference service_types
		await migrationClient`
			UPDATE clinic_services
			SET service_type_id = st.id
			FROM service_types st
			WHERE clinic_services.name = st.name
			AND clinic_services.service_type_id IS NULL
		`;

		console.log("Step 7: Handling unmapped services...");

		// For services that don't have a matching service type, create new service types
		const unmappedServices = await migrationClient`
			SELECT DISTINCT name, description, category, requires_appointment
			FROM clinic_services
			WHERE service_type_id IS NULL
			AND name IS NOT NULL
			AND description IS NOT NULL
		`;

		for (const service of unmappedServices) {
			// Create service type for unmapped service
			const [newServiceType] = await migrationClient`
				INSERT INTO service_types (name, description, category, requires_appointment, created_by)
				VALUES (${service.name}, ${service.description}, ${service.category || "General"}, ${service.requires_appointment || false}, ${adminUserId})
				RETURNING id
			`;

			// Update clinic_services to reference the new service type
			await migrationClient`
				UPDATE clinic_services
				SET service_type_id = ${newServiceType.id}
				WHERE name = ${service.name} AND service_type_id IS NULL
			`;
		}

		console.log("Step 8: Removing old columns and adding constraints...");

		// Remove old columns that are now redundant (separate statements)
		await migrationClient`ALTER TABLE clinic_services DROP COLUMN IF EXISTS name`;
		await migrationClient`ALTER TABLE clinic_services DROP COLUMN IF EXISTS description`;
		await migrationClient`ALTER TABLE clinic_services DROP COLUMN IF EXISTS category`;
		await migrationClient`ALTER TABLE clinic_services DROP COLUMN IF EXISTS requires_appointment`;

		// Add custom_description column for clinic-specific descriptions
		await migrationClient`ALTER TABLE clinic_services ADD COLUMN IF NOT EXISTS custom_description TEXT`;

		// Make service_type_id NOT NULL
		await migrationClient`ALTER TABLE clinic_services ALTER COLUMN service_type_id SET NOT NULL`;

		console.log("Step 9: Creating new indexes...");

		// Create new indexes for the updated clinic_services table (separate statements)
		await migrationClient`CREATE INDEX IF NOT EXISTS idx_clinic_services_service_type_id ON clinic_services(service_type_id)`;
		await migrationClient`CREATE INDEX IF NOT EXISTS idx_clinic_services_clinic_type ON clinic_services(clinic_id, service_type_id)`;
		await migrationClient`CREATE UNIQUE INDEX IF NOT EXISTS idx_clinic_services_unique ON clinic_services(clinic_id, service_type_id)`;

		console.log("Step 10: Verifying migration...");

		// Verify the migration
		const serviceTypeCount = await migrationClient`
			SELECT COUNT(*) as count FROM service_types
		`;

		const clinicServiceCount = await migrationClient`
			SELECT COUNT(*) as count FROM clinic_services WHERE service_type_id IS NOT NULL
		`;

		console.log(`Migration completed successfully!`);
		console.log(`- Created ${serviceTypeCount[0].count} service types`);
		console.log(`- Updated ${clinicServiceCount[0].count} clinic services`);
		console.log(
			`- Backup table 'clinic_services_backup' created for safety`
		);
	} catch (error) {
		console.error("Migration failed:", error);
		process.exit(1);
	} finally {
		await migrationClient.end();
	}
}

// Run the migration
if (import.meta.main) {
	migrateServiceTypes();
}

export { migrateServiceTypes };
