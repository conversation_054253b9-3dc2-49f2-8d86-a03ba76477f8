import { migrate } from "drizzle-orm/postgres-js/migrator";
import postgres from "postgres";
import { drizzle } from "drizzle-orm/postgres-js";

// This script runs the migrations
async function main() {
	console.log("Running migrations...");

	const connectionString =
		process.env.DATABASE_URL ||
		"postgresql://postgres:postgres@localhost:5432/cat_adoption_db";

	// Create connection
	const migrationClient = postgres(connectionString, { max: 1 });

	try {
		// Initialize drizzle with the migration client
		const db = drizzle(migrationClient);

		// Run migrations
		await migrate(db, { migrationsFolder: "drizzle/migrations" });

		console.log("Migrations completed successfully!");
	} catch (error) {
		console.error("Migration failed:", error);
		process.exit(1);
	} finally {
		// Close connection
		await migrationClient.end();
	}
}

main();
