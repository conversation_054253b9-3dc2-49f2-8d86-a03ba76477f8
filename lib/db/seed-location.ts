import { db } from "./index";
import { wilayas, communes } from "./schema";
import { getCommunesForWilaya } from "./algeria-communes-data";

// This list will be used both for creating wilayas and as a reference for commune names
const wilayasList = [
	{ code: "01", name: "Ad<PERSON>", nameAr: "أدرار", nameFr: "Adrar" },
	{ code: "02", name: "<PERSON>le<PERSON>", nameAr: "الشلف", nameFr: "Chle<PERSON>" },
	{ code: "03", name: "Laghou<PERSON>", nameAr: "الأغواط", nameFr: "Laghouat" },
	{
		code: "04",
		name: "<PERSON>um El Bouaghi",
		nameAr: "أم البواقي",
		nameFr: "Oum El Bouaghi",
	},
	{ code: "05", name: "<PERSON><PERSON>", nameAr: "باتنة", nameFr: "<PERSON><PERSON>" },
	{ code: "06", name: "<PERSON><PERSON><PERSON><PERSON><PERSON>", nameAr: "بجاية", nameFr: "<PERSON><PERSON><PERSON><PERSON><PERSON>" },
	{ code: "07", name: "<PERSON><PERSON><PERSON>", nameAr: "بس<PERSON><PERSON>ة", nameFr: "<PERSON><PERSON><PERSON>" },
	{ code: "08", name: "<PERSON><PERSON><PERSON><PERSON>", nameAr: "بشار", nameFr: "Béchar" },
	{ code: "09", name: "Blida", nameAr: "البليدة", nameFr: "Blida" },
	{ code: "10", name: "Bouira", nameAr: "البويرة", nameFr: "Bouira" },
	{
		code: "11",
		name: "Tamanrasset",
		nameAr: "تمنراست",
		nameFr: "Tamanrasset",
	},
	{ code: "12", name: "Tébessa", nameAr: "تبسة", nameFr: "Tébessa" },
	{ code: "13", name: "Tlemcen", nameAr: "تلمسان", nameFr: "Tlemcen" },
	{ code: "14", name: "Tiaret", nameAr: "تيارت", nameFr: "Tiaret" },
	{
		code: "15",
		name: "Tizi Ouzou",
		nameAr: "تيزي وزو",
		nameFr: "Tizi Ouzou",
	},
	{ code: "16", name: "Algiers", nameAr: "الجزائر", nameFr: "Alger" },
	{ code: "17", name: "Djelfa", nameAr: "الجلفة", nameFr: "Djelfa" },
	{ code: "18", name: "Jijel", nameAr: "جيجل", nameFr: "Jijel" },
	{ code: "19", name: "Sétif", nameAr: "سطيف", nameFr: "Sétif" },
	{ code: "20", name: "Saïda", nameAr: "سعيدة", nameFr: "Saïda" },
	{ code: "21", name: "Skikda", nameAr: "سكيكدة", nameFr: "Skikda" },
	{
		code: "22",
		name: "Sidi Bel Abbès",
		nameAr: "سيدي بلعباس",
		nameFr: "Sidi Bel Abbès",
	},
	{ code: "23", name: "Annaba", nameAr: "عنابة", nameFr: "Annaba" },
	{ code: "24", name: "Guelma", nameAr: "قالمة", nameFr: "Guelma" },
	{
		code: "25",
		name: "Constantine",
		nameAr: "قسنطينة",
		nameFr: "Constantine",
	},
	{ code: "26", name: "Médéa", nameAr: "المدية", nameFr: "Médéa" },
	{
		code: "27",
		name: "Mostaganem",
		nameAr: "مستغانم",
		nameFr: "Mostaganem",
	},
	{ code: "28", name: "M'Sila", nameAr: "المسيلة", nameFr: "M'Sila" },
	{ code: "29", name: "Mascara", nameAr: "معسكر", nameFr: "Mascara" },
	{ code: "30", name: "Ouargla", nameAr: "ورقلة", nameFr: "Ouargla" },
	{ code: "31", name: "Oran", nameAr: "وهران", nameFr: "Oran" },
	{ code: "32", name: "El Bayadh", nameAr: "البيض", nameFr: "El Bayadh" },
	{ code: "33", name: "Illizi", nameAr: "إليزي", nameFr: "Illizi" },
	{
		code: "34",
		name: "Bordj Bou Arréridj",
		nameAr: "برج بوعريريج",
		nameFr: "Bordj Bou Arréridj",
	},
	{
		code: "35",
		name: "Boumerdès",
		nameAr: "بومرداس",
		nameFr: "Boumerdès",
	},
	{ code: "36", name: "El Tarf", nameAr: "الطارف", nameFr: "El Tarf" },
	{ code: "37", name: "Tindouf", nameAr: "تندوف", nameFr: "Tindouf" },
	{
		code: "38",
		name: "Tissemsilt",
		nameAr: "تيسمسيلت",
		nameFr: "Tissemsilt",
	},
	{ code: "39", name: "El Oued", nameAr: "الوادي", nameFr: "El Oued" },
	{ code: "40", name: "Khenchela", nameAr: "خنشلة", nameFr: "Khenchela" },
	{
		code: "41",
		name: "Souk Ahras",
		nameAr: "سوق أهراس",
		nameFr: "Souk Ahras",
	},
	{ code: "42", name: "Tipaza", nameAr: "تيبازة", nameFr: "Tipaza" },
	{ code: "43", name: "Mila", nameAr: "ميلة", nameFr: "Mila" },
	{
		code: "44",
		name: "Aïn Defla",
		nameAr: "عين الدفلى",
		nameFr: "Aïn Defla",
	},
	{ code: "45", name: "Naâma", nameAr: "النعامة", nameFr: "Naâma" },
	{
		code: "46",
		name: "Aïn Témouchent",
		nameAr: "عين تموشنت",
		nameFr: "Aïn Témouchent",
	},
	{ code: "47", name: "Ghardaïa", nameAr: "غرداية", nameFr: "Ghardaïa" },
	{ code: "48", name: "Relizane", nameAr: "غليزان", nameFr: "Relizane" },
	{
		code: "49",
		name: "El M'Ghair",
		nameAr: "المغير",
		nameFr: "El M'Ghair",
	},
	{
		code: "50",
		name: "El Meniaa",
		nameAr: "المنيعة",
		nameFr: "El Meniaa",
	},
	{
		code: "51",
		name: "Ouled Djellal",
		nameAr: "أولاد جلال",
		nameFr: "Ouled Djellal",
	},
	{
		code: "52",
		name: "Bordj Baji Mokhtar",
		nameAr: "برج باجي مختار",
		nameFr: "Bordj Baji Mokhtar",
	},
	{
		code: "53",
		name: "Béni Abbès",
		nameAr: "بني عباس",
		nameFr: "Béni Abbès",
	},
	{ code: "54", name: "Timimoun", nameAr: "تيميمون", nameFr: "Timimoun" },
	{ code: "55", name: "Touggourt", nameAr: "تقرت", nameFr: "Touggourt" },
	{ code: "56", name: "Djanet", nameAr: "جانت", nameFr: "Djanet" },
	{
		code: "57",
		name: "In Salah",
		nameAr: "عين صالح",
		nameFr: "In Salah",
	},
	{
		code: "58",
		name: "In Guezzam",
		nameAr: "عين قزام",
		nameFr: "In Guezzam",
	},
];

async function seedAlgerianWilayas() {
	console.log("🇩🇿 Seeding Algerian wilayas...");

	const wilayaMap = new Map();

	for (const wilaya of wilayasList) {
		const [insertedWilaya] = await db
			.insert(wilayas)
			.values({
				code: wilaya.code,
				name: wilaya.name,
				nameAr: wilaya.nameAr,
				nameFr: wilaya.nameFr,
				createdAt: new Date(),
				updatedAt: new Date(),
			})
			.returning();

		wilayaMap.set(wilaya.code, insertedWilaya.id);
	}

	console.log("✅ Algerian wilayas seeded successfully!");
	return wilayaMap;
}

async function seedAlgerianCommunes(wilayaMap: Map<string, number>) {
	console.log("🏙️ Seeding Algerian communes...");

	// For each wilaya code from 01 to 58 (all 58 wilayas)
	for (let i = 1; i <= 58; i++) {
		const wilayaCode = i.toString().padStart(2, "0");
		const wilayaId = wilayaMap.get(wilayaCode);

		if (!wilayaId) {
			console.warn(`⚠️ Could not find wilaya ID for code ${wilayaCode}`);
			continue;
		}

		// Get communes for this wilaya from our data source
		const communesList = getCommunesForWilaya(wilayaCode);

		if (communesList.length > 0) {
			// If we have communes data for this wilaya, insert them
			for (const commune of communesList) {
				await db.insert(communes).values({
					name: commune.name,
					nameAr: commune.nameAr,
					nameFr: commune.nameFr,
					wilayaId: wilayaId,
					createdAt: new Date(),
					updatedAt: new Date(),
				});
			}
			console.log(
				`✅ Communes for wilaya ${wilayaCode} seeded successfully! (${communesList.length} communes)`
			);
		} else {
			// If no communes data found for this wilaya, add at least the capital city
			// Lookup the wilaya name to use as a default commune
			const wilayaData = wilayasList.find((w) => w.code === wilayaCode);

			if (wilayaData) {
				await db.insert(communes).values({
					name: wilayaData.name,
					nameAr: wilayaData.nameAr,
					nameFr: wilayaData.nameFr,
					wilayaId: wilayaId,
					createdAt: new Date(),
					updatedAt: new Date(),
				});
				console.log(
					`⚠️ Added capital commune for wilaya ${wilayaCode} (${wilayaData.name})`
				);
			}
		}
	}

	console.log("✅ Algerian communes seeded successfully!");
}

export async function seedLocation() {
	try {
		const wilayaMap = await seedAlgerianWilayas();
		await seedAlgerianCommunes(wilayaMap);

		console.log("✅ All location data seeded successfully!");
		return true;
	} catch (error) {
		console.error("❌ Seeding failed:", error);
		throw error;
	}
}

// If this file is run directly, execute the seed function
if (require.main === module) {
	seedLocation()
		.then(() => {
			process.exit(0);
		})
		.catch((error: unknown) => {
			console.error("❌ Unhandled error during seeding:", error);
			process.exit(1);
		});
}
