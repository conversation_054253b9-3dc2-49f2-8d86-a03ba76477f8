#!/usr/bin/env tsx

import { drizzle } from "drizzle-orm/postgres-js";
import postgres from "postgres";
import { config } from "dotenv";

// Load environment variables
config({ path: ".env.local" });

/**
 * Migration script to create the clinic_services table
 * This adds detailed service management capabilities to the clinic system
 */
async function migrateClinicServices() {
	const connectionString = process.env.DATABASE_URL;
	if (!connectionString) {
		throw new Error("DATABASE_URL environment variable is not set");
	}

	// Create a separate client for migration
	const migrationClient = postgres(connectionString, { max: 1 });

	try {
		// Initialize drizzle with the migration client
		const db = drizzle(migrationClient);

		console.log("Step 1: Creating clinic_services table...");
		
		// Create the clinic_services table
		await migrationClient`
			CREATE TABLE IF NOT EXISTS clinic_services (
				id SERIAL PRIMARY KEY,
				clinic_id INTEGER NOT NULL REFERENCES clinic_profiles(id) ON DELETE CASCADE,
				name TEXT NOT NULL,
				description TEXT NOT NULL,
				price TEXT,
				is_available BOOLEAN DEFAULT true NOT NULL,
				requires_appointment BOOLEAN DEFAULT false NOT NULL,
				category TEXT,
				display_order INTEGER DEFAULT 0 NOT NULL,
				created_at TIMESTAMP DEFAULT NOW() NOT NULL,
				updated_at TIMESTAMP DEFAULT NOW() NOT NULL
			)
		`;

		console.log("Step 2: Creating indexes for clinic_services...");
		
		// Create indexes for performance
		await migrationClient`
			CREATE INDEX IF NOT EXISTS idx_clinic_services_clinic_id 
			ON clinic_services(clinic_id)
		`;

		await migrationClient`
			CREATE INDEX IF NOT EXISTS idx_clinic_services_available 
			ON clinic_services(is_available)
		`;

		await migrationClient`
			CREATE INDEX IF NOT EXISTS idx_clinic_services_category 
			ON clinic_services(category)
		`;

		await migrationClient`
			CREATE INDEX IF NOT EXISTS idx_clinic_services_order 
			ON clinic_services(display_order)
		`;

		await migrationClient`
			CREATE INDEX IF NOT EXISTS idx_clinic_services_clinic_available 
			ON clinic_services(clinic_id, is_available)
		`;

		console.log("Step 3: Migrating existing services data...");
		
		// Migrate existing services from clinic_profiles.services array to clinic_services table
		const clinicsWithServices = await migrationClient`
			SELECT id, services 
			FROM clinic_profiles 
			WHERE services IS NOT NULL AND array_length(services, 1) > 0
		`;

		for (const clinic of clinicsWithServices) {
			if (clinic.services && Array.isArray(clinic.services)) {
				for (let i = 0; i < clinic.services.length; i++) {
					const serviceName = clinic.services[i];
					if (serviceName && serviceName.trim()) {
						await migrationClient`
							INSERT INTO clinic_services (
								clinic_id, 
								name, 
								description, 
								display_order,
								created_at,
								updated_at
							) VALUES (
								${clinic.id},
								${serviceName.trim()},
								${`Professional ${serviceName.toLowerCase()} service for cats.`},
								${i},
								NOW(),
								NOW()
							)
						`;
					}
				}
			}
		}

		console.log("Step 4: Adding updated_at trigger for clinic_services...");
		
		// Create trigger to automatically update updated_at timestamp
		await migrationClient`
			CREATE OR REPLACE FUNCTION update_clinic_services_updated_at()
			RETURNS TRIGGER AS $$
			BEGIN
				NEW.updated_at = NOW();
				RETURN NEW;
			END;
			$$ language 'plpgsql'
		`;

		await migrationClient`
			DROP TRIGGER IF EXISTS update_clinic_services_updated_at_trigger ON clinic_services
		`;

		await migrationClient`
			CREATE TRIGGER update_clinic_services_updated_at_trigger
			BEFORE UPDATE ON clinic_services
			FOR EACH ROW
			EXECUTE FUNCTION update_clinic_services_updated_at()
		`;

		console.log("✅ Clinic services migration completed successfully!");
		console.log("📊 Migration summary:");
		
		// Get migration statistics
		const serviceCount = await migrationClient`
			SELECT COUNT(*) as count FROM clinic_services
		`;
		
		const clinicCount = await migrationClient`
			SELECT COUNT(*) as count FROM clinic_profiles WHERE services IS NOT NULL
		`;

		console.log(`   - Created clinic_services table with proper indexes`);
		console.log(`   - Migrated services from ${clinicCount[0]?.count || 0} clinic profiles`);
		console.log(`   - Total services created: ${serviceCount[0]?.count || 0}`);
		console.log(`   - Added automatic updated_at trigger`);

	} catch (error) {
		console.error("❌ Migration failed:", error);
		throw error;
	} finally {
		await migrationClient.end();
	}
}

// Run migration if this file is executed directly
if (require.main === module) {
	migrateClinicServices()
		.then(() => {
			console.log("🎉 Migration completed successfully!");
			process.exit(0);
		})
		.catch((error) => {
			console.error("💥 Migration failed:", error);
			process.exit(1);
		});
}

export { migrateClinicServices };
