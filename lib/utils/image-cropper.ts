/**
 * Creates a cropped image from a source image
 * @param imageSrc - The source image URL or data URL
 * @param pixelCrop - The crop area in pixels
 * @returns A Promise that resolves to a Blob of the cropped image
 */
export async function getCroppedImg(
	imageSrc: string,
	pixelCrop: { x: number; y: number; width: number; height: number }
): Promise<Blob> {
	const image = await createImage(imageSrc);
	const canvas = document.createElement("canvas");
	const ctx = canvas.getContext("2d");

	if (!ctx) {
		throw new Error("No 2d context");
	}

	// Set canvas size to the cropped size
	canvas.width = pixelCrop.width;
	canvas.height = pixelCrop.height;

	// Draw the cropped image onto the canvas
	ctx.drawImage(
		image,
		pixelCrop.x,
		pixelCrop.y,
		pixelCrop.width,
		pixelCrop.height,
		0,
		0,
		pixelCrop.width,
		pixelCrop.height
	);

	// As a Blob
	return new Promise((resolve, reject) => {
		canvas.toBlob(
			(blob) => {
				if (!blob) {
					reject(new Error("Canvas is empty"));
					return;
				}
				resolve(blob);
			},
			"image/jpeg",
			0.95
		); // JPEG at 95% quality
	});
}

/**
 * Creates an Image object from a source URL
 * @param url - The source image URL
 * @returns A Promise that resolves to an Image object
 */
function createImage(url: string): Promise<HTMLImageElement> {
	return new Promise((resolve, reject) => {
		const image = new Image();
		image.addEventListener("load", () => resolve(image));
		image.addEventListener("error", (error) => reject(error));
		image.src = url;
	});
}
