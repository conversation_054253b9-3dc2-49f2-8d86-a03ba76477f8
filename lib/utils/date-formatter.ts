/**
 * Formats a timestamp into a human-readable format based on how recent it is
 * @param timestamp ISO string or Date object
 * @returns Formatted time string (e.g., "12:30 PM", "Yesterday", "Mon", "Jan 15", "Jan 15, 2023")
 */
export function formatRelativeTime(timestamp: string | Date): string {
	const date = timestamp instanceof Date ? timestamp : new Date(timestamp);
	const now = new Date();

	// Today
	if (date.toDateString() === now.toDateString()) {
		return date.toLocaleTimeString([], {
			hour: "2-digit",
			minute: "2-digit",
		});
	}

	// Yesterday
	const yesterday = new Date(now);
	yesterday.setDate(now.getDate() - 1);
	if (date.toDateString() === yesterday.toDateString()) {
		return "Yesterday";
	}

	// This week (within 7 days)
	const oneWeekAgo = new Date(now);
	oneWeekAgo.setDate(now.getDate() - 7);
	if (date > oneWeekAgo) {
		return date.toLocaleDateString([], { weekday: "short" });
	}

	// This year
	if (date.getFullYear() === now.getFullYear()) {
		return date.toLocaleDateString([], { month: "short", day: "numeric" });
	}

	// Older
	return date.toLocaleDateString([], {
		year: "numeric",
		month: "short",
		day: "numeric",
	});
}
