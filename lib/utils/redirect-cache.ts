/**
 * Shared cache utilities for redirect API routes
 * This provides a centralized way to manage caching for legacy URL redirects
 */

export interface CacheEntry {
	slug: string;
	timestamp: number;
}

export class RedirectCache {
	private cache = new Map<string, CacheEntry>();
	private ttl: number;
	private cleanupInterval: NodeJS.Timeout | null = null;

	constructor(ttlMinutes: number = 5) {
		this.ttl = ttlMinutes * 60 * 1000; // Convert to milliseconds
		this.startCleanup();
	}

	/**
	 * Get a cached entry if it exists and is not expired
	 */
	get(key: string): string | null {
		const entry = this.cache.get(key);
		if (!entry) {
			return null;
		}

		// Check if expired
		if (Date.now() - entry.timestamp > this.ttl) {
			this.cache.delete(key);
			return null;
		}

		return entry.slug;
	}

	/**
	 * Set a cache entry
	 */
	set(key: string, slug: string): void {
		this.cache.set(key, {
			slug,
			timestamp: Date.now(),
		});
	}

	/**
	 * Check if a key exists in cache (and is not expired)
	 */
	has(key: string): boolean {
		return this.get(key) !== null;
	}

	/**
	 * Clear a specific cache entry
	 */
	delete(key: string): boolean {
		return this.cache.delete(key);
	}

	/**
	 * Clear all cache entries
	 */
	clear(): void {
		this.cache.clear();
	}

	/**
	 * Get cache statistics
	 */
	getStats() {
		const now = Date.now();
		const entries = Array.from(this.cache.entries()).map(
			([key, entry]) => ({
				key,
				slug: entry.slug,
				age: now - entry.timestamp,
				expired: now - entry.timestamp > this.ttl,
			})
		);

		return {
			size: this.cache.size,
			ttl: this.ttl,
			entries,
		};
	}

	/**
	 * Manually clean up expired entries
	 */
	cleanup(): number {
		const now = Date.now();
		let cleaned = 0;

		for (const [key, entry] of this.cache.entries()) {
			if (now - entry.timestamp > this.ttl) {
				this.cache.delete(key);
				cleaned++;
			}
		}

		return cleaned;
	}

	/**
	 * Start automatic cleanup interval
	 */
	private startCleanup(): void {
		if (this.cleanupInterval) {
			clearInterval(this.cleanupInterval);
		}

		// Clean up every minute
		this.cleanupInterval = setInterval(() => {
			const cleaned = this.cleanup();
			if (cleaned > 0) {
				console.log(
					`[RedirectCache] Cleaned up ${cleaned} expired entries`
				);
			}
		}, 60 * 1000);
	}

	/**
	 * Stop automatic cleanup (useful for testing or shutdown)
	 */
	stopCleanup(): void {
		if (this.cleanupInterval) {
			clearInterval(this.cleanupInterval);
			this.cleanupInterval = null;
		}
	}
}

// Singleton instances for different types of redirects
export const catRedirectCache = new RedirectCache(5); // 5 minutes TTL
export const userRedirectCache = new RedirectCache(5); // 5 minutes TTL

// Utility functions for common cache operations
export function getCatSlug(id: number): string | null {
	return catRedirectCache.get(`cat-${id}`);
}

export function setCatSlug(id: number, slug: string): void {
	catRedirectCache.set(`cat-${id}`, slug);
}

export function getUserSlug(id: number): string | null {
	return userRedirectCache.get(`user-${id}`);
}

export function setUserSlug(id: number, slug: string): void {
	userRedirectCache.set(`user-${id}`, slug);
}

// Export cache stats for monitoring
export function getAllCacheStats() {
	return {
		cats: catRedirectCache.getStats(),
		users: userRedirectCache.getStats(),
	};
}
