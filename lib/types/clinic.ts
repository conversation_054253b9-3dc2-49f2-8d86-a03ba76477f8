import { z } from "zod";

// Operating Hours Types
export interface OperatingHours {
	monday?: string;
	tuesday?: string;
	wednesday?: string;
	thursday?: string;
	friday?: string;
	saturday?: string;
	sunday?: string;
}

// Service Categories and Tags
export const SERVICE_CATEGORIES = {
	MEDICAL: "medical",
	SURGICAL: "surgical",
	PREVENTIVE: "preventive",
	EMERGENCY: "emergency",
	GROOMING: "grooming",
	BOARDING: "boarding",
} as const;

export type ServiceCategory =
	(typeof SERVICE_CATEGORIES)[keyof typeof SERVICE_CATEGORIES];

// Common veterinary services
export const COMMON_SERVICES = [
	"Vaccinations",
	"Spay/Neuter",
	"Microchipping",
	"Health Checkups",
	"Dental Care",
	"Emergency Care",
	"Surgery",
	"X-rays",
	"Blood Tests",
	"Grooming",
	"Boarding",
	"Behavioral Consultation",
	"Nutrition Counseling",
	"Parasite Treatment",
	"Wound Care",
] as const;

export type CommonService = (typeof COMMON_SERVICES)[number];

// Service Type Interface (Admin-managed service definitions)
export interface ServiceType {
	id: number;
	name: string;
	description: string;
	category: string;
	isActive: boolean;
	requiresAppointment: boolean;
	displayOrder: number;
	createdAt: Date | string;
	updatedAt: Date | string;
	createdBy: number;
}

// Service Type with Creator Information
export interface ServiceTypeWithCreator extends ServiceType {
	createdBy: {
		id: number;
		name: string;
		email: string;
	};
}

// Updated Clinic Service Interface (references service types)
export interface ClinicService {
	id: number;
	clinicId: number;
	serviceTypeId: number;
	price?: string | null;
	isAvailable: boolean;
	customDescription?: string | null;
	displayOrder: number;
	createdAt: Date | string;
	updatedAt: Date | string;
}

// Clinic Service with Service Type Information
export interface ClinicServiceWithType extends ClinicService {
	serviceType: ServiceType;
}

// Service with Clinic Information (updated)
export interface ServiceWithClinic extends ClinicServiceWithType {
	clinic: {
		id: number;
		name: string;
		slug: string;
	};
}

// Clinic Profile Interface
// Clinic Status Types
export const CLINIC_STATUS = {
	PENDING: "pending",
	APPROVED: "approved",
	REJECTED: "rejected",
} as const;

export type ClinicStatus = (typeof CLINIC_STATUS)[keyof typeof CLINIC_STATUS];

export interface ClinicProfile {
	id: number;
	userId: number;
	name: string;
	slug: string;
	address: string;
	wilayaId?: number | null;
	communeId?: number | null;
	phone: string;
	website?: string | null;
	services?: string[] | null;
	operatingHours?: OperatingHours | null;
	featured: boolean;
	status: ClinicStatus;
	createdAt: Date | string;
	updatedAt: Date | string;
}

// Clinic with User Information
export interface ClinicWithUser extends ClinicProfile {
	user: {
		id: number;
		name: string;
		email: string;
		slug: string;
		image?: string | null;
		emailVerified: boolean;
	};
}

// Clinic Summary for listings
export interface ClinicSummary {
	id: number;
	name: string;
	wilayaId?: number | null;
	communeId?: number | null;
	services?: string[] | null;
	featured: boolean;
	slug: string;
	image?: string | null;
}

// Zod Validation Schemas

// Operating Hours Schema
export const operatingHoursSchema = z.object({
	monday: z.string().optional(),
	tuesday: z.string().optional(),
	wednesday: z.string().optional(),
	thursday: z.string().optional(),
	friday: z.string().optional(),
	saturday: z.string().optional(),
	sunday: z.string().optional(),
});

// Clinic Profile Creation Schema
export const clinicProfileCreateSchema = z.object({
	name: z
		.string()
		.min(2, "Clinic name must be at least 2 characters")
		.max(100),
	address: z
		.string()
		.min(5, "Address must be at least 5 characters")
		.max(200),
	wilayaId: z.number().optional(),
	communeId: z.number().optional(),
	phone: z
		.string()
		.min(8, "Phone number must be at least 8 characters")
		.max(20),
	website: z
		.string()
		.refine((val) => val === "" || /^https?:\/\/.+/.test(val), {
			message: "Must be a valid URL",
		})
		.optional(),
	services: z.array(z.string()).optional(),
	operatingHours: operatingHoursSchema.optional(),
});

// Clinic Profile Update Schema (includes slug for internal updates)
export const clinicProfileUpdateSchema = clinicProfileCreateSchema
	.partial()
	.extend({
		slug: z.string().optional(), // Allow slug updates for internal use
	});

// Clinic Search/Filter Schema
export const clinicSearchSchema = z.object({
	query: z.string().optional(),
	wilayaId: z.number().optional(),
	communeId: z.number().optional(),
	services: z.array(z.string()).optional(),
	featured: z.boolean().optional(),
	limit: z.number().min(1).max(100).default(20),
	offset: z.number().min(0).default(0),
});

// Admin Clinic Management Schema
export const adminClinicUpdateSchema = z.object({
	featured: z.boolean().optional(),
	status: z.enum(["pending", "approved", "rejected"]).optional(),
});

// Clinic Status Update Schema
export const clinicStatusUpdateSchema = z.object({
	clinicId: z.number(),
	status: z.enum(["pending", "approved", "rejected"]),
});

// Service Type Management Schemas (Admin-only)

// Service Type Creation Schema
export const serviceTypeCreateSchema = z.object({
	name: z
		.string()
		.min(2, "Service type name must be at least 2 characters")
		.max(100, "Service type name must be less than 100 characters"),
	description: z
		.string()
		.min(10, "Description must be at least 10 characters")
		.max(500, "Description must be less than 500 characters"),
	category: z
		.string()
		.min(2, "Category must be at least 2 characters")
		.max(50, "Category must be less than 50 characters"),
	isActive: z.boolean().default(true),
	requiresAppointment: z.boolean().default(false),
	displayOrder: z.number().min(0).default(0),
});

// Service Type Update Schema
export const serviceTypeUpdateSchema = serviceTypeCreateSchema.partial();

// Service Type Filter Schema
export const serviceTypeFilterSchema = z.object({
	category: z.string().optional(),
	isActive: z.boolean().optional(),
	search: z.string().optional(),
	limit: z.number().min(1).max(100).default(20),
	offset: z.number().min(0).default(0),
});

// Clinic Service Management Schemas (Updated for two-tier system)

// Clinic Service Selection Schema (clinics select from existing service types)
export const clinicServiceCreateSchema = z.object({
	serviceTypeId: z.number().min(1, "Service type is required"),
	price: z
		.string()
		.max(50, "Price must be less than 50 characters")
		.optional(),
	isAvailable: z.boolean().default(true),
	customDescription: z
		.string()
		.max(500, "Custom description must be less than 500 characters")
		.optional(),
	displayOrder: z.number().min(0).default(0),
});

// Clinic Service Update Schema
export const clinicServiceUpdateSchema = clinicServiceCreateSchema.partial();

// Clinic Service Filter Schema (Updated)
export const clinicServiceFilterSchema = z.object({
	clinicId: z.number().optional(),
	serviceTypeId: z.number().optional(),
	category: z.string().optional(),
	isAvailable: z.boolean().optional(),
	requiresAppointment: z.boolean().optional(),
	limit: z.number().min(1).max(100).default(20),
	offset: z.number().min(0).default(0),
});

// Type exports for form validation
export type ClinicProfileCreateInput = z.infer<
	typeof clinicProfileCreateSchema
>;
export type ClinicProfileUpdateInput = z.infer<
	typeof clinicProfileUpdateSchema
>;
export type ClinicSearchInput = z.infer<typeof clinicSearchSchema>;
export type AdminClinicUpdateInput = z.infer<typeof adminClinicUpdateSchema>;

// Service Type exports (Admin-only)
export type ServiceTypeCreateInput = z.infer<typeof serviceTypeCreateSchema>;
export type ServiceTypeUpdateInput = z.infer<typeof serviceTypeUpdateSchema>;
export type ServiceTypeFilterInput = z.infer<typeof serviceTypeFilterSchema>;

// Clinic Service exports (Updated)
export type ClinicServiceCreateInput = z.infer<
	typeof clinicServiceCreateSchema
>;
export type ClinicServiceUpdateInput = z.infer<
	typeof clinicServiceUpdateSchema
>;
export type ClinicServiceFilterInput = z.infer<
	typeof clinicServiceFilterSchema
>;

// Clinic Application Status
export const CLINIC_APPLICATION_STATUS = {
	PENDING: "pending",
	APPROVED: "approved",
	REJECTED: "rejected",
} as const;

export type ClinicApplicationStatus =
	(typeof CLINIC_APPLICATION_STATUS)[keyof typeof CLINIC_APPLICATION_STATUS];

// Time slot validation helper
export const timeSlotRegex =
	/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]-([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/;

export const validateTimeSlot = (timeSlot: string): boolean => {
	if (!timeSlotRegex.test(timeSlot)) return false;

	const [start, end] = timeSlot.split("-");
	const [startHour, startMin] = start.split(":").map(Number);
	const [endHour, endMin] = end.split(":").map(Number);

	const startTime = startHour * 60 + startMin;
	const endTime = endHour * 60 + endMin;

	return endTime > startTime;
};

// Helper function to format operating hours for display
export const formatOperatingHours = (
	hours: OperatingHours | null | undefined
): string => {
	if (!hours) return "Hours not specified";

	const days = [
		"monday",
		"tuesday",
		"wednesday",
		"thursday",
		"friday",
		"saturday",
		"sunday",
	];
	const dayNames = ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"];

	const formattedHours = days
		.map((day, index) => {
			const dayHours = hours[day as keyof OperatingHours];
			return dayHours ? `${dayNames[index]}: ${dayHours}` : null;
		})
		.filter(Boolean);

	return formattedHours.length > 0
		? formattedHours.join(", ")
		: "Hours not specified";
};

// Helper function to check if clinic is currently open
export const isClinicOpen = (
	hours: OperatingHours | null | undefined
): boolean => {
	if (!hours) return false;

	const now = new Date();
	const dayNames = [
		"sunday",
		"monday",
		"tuesday",
		"wednesday",
		"thursday",
		"friday",
		"saturday",
	];
	const currentDay = dayNames[now.getDay()];
	const currentTime = now.getHours() * 60 + now.getMinutes();

	const todayHours = hours[currentDay as keyof OperatingHours];
	if (!todayHours) return false;

	const [start, end] = todayHours.split("-");
	const [startHour, startMin] = start.split(":").map(Number);
	const [endHour, endMin] = end.split(":").map(Number);

	const startTime = startHour * 60 + startMin;
	const endTime = endHour * 60 + endMin;

	return currentTime >= startTime && currentTime <= endTime;
};
