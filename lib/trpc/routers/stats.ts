import { createTRPCRouter as router, publicProcedure } from "../trpc";
import { cats, users, clinicProfiles } from "@/lib/db/schema";
import { eq, count } from "drizzle-orm";
import { logSlowQuery } from "./helpers/cat-helpers";

export const statsRouter = router({
	getOverview: publicProcedure.query(async ({ ctx }) => {
		const startTime = performance.now();

		// Get real stats from database
		const [adoptedCatsResult] = await ctx.db
			.select({ value: count() })
			.from(cats)
			.where(eq(cats.adopted, true));

		const [availableCatsResult] = await ctx.db
			.select({ value: count() })
			.from(cats)
			.where(eq(cats.adopted, false));

		const [registeredUsersResult] = await ctx.db
			.select({ value: count() })
			.from(users);

		const [clinicsResult] = await ctx.db
			.select({ value: count() })
			.from(clinicProfiles);

		const duration = performance.now() - startTime;
		logSlowQuery("statsOverview", duration);

		return {
			adoptedCats: adoptedCatsResult.value,
			availableCats: availableCatsResult.value,
			registeredUsers: registeredUsersResult.value,
			clinics: clinicsResult.value,
		};
	}),
});
