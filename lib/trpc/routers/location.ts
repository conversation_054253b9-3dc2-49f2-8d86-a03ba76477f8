import { z } from "zod";
import { createTRPCRouter as router, publicProcedure } from "../trpc";
import { wilayas, communes } from "@/lib/db/schema";
import { eq } from "drizzle-orm";
import { logSlowQuery } from "./helpers/cat-helpers";

export const locationRouter = router({
	getWilayas: publicProcedure.query(async ({ ctx }) => {
		const startTime = performance.now();

		const wilayasList = await ctx.db.query.wilayas.findMany({
			orderBy: (wilayas, { asc }) => [asc(wilayas.code)],
		});

		const duration = performance.now() - startTime;
		logSlowQuery("getWilayas", duration);

		return wilayasList;
	}),

	getWilayaById: publicProcedure
		.input(z.number())
		.query(async ({ ctx, input }) => {
			const wilaya = await ctx.db.query.wilayas.findFirst({
				where: (wilayas, { eq }) => eq(wilayas.id, input),
			});

			return wilaya;
		}),

	getCommunesByWilaya: publicProcedure
		.input(
			z.object({
				wilayaId: z.number().optional(),
			})
		)
		.query(async ({ ctx, input }) => {
			const startTime = performance.now();

			if (!input.wilayaId) {
				return [];
			}

			const communesList = await ctx.db.query.communes.findMany({
				where: eq(communes.wilayaId, input.wilayaId),
				orderBy: (communes, { asc }) => [asc(communes.id)],
			});

			const duration = performance.now() - startTime;
			logSlowQuery("getCommunesByWilaya", duration);

			return communesList;
		}),

	getCommuneById: publicProcedure
		.input(z.number())
		.query(async ({ ctx, input }) => {
			const commune = await ctx.db.query.communes.findFirst({
				where: (communes, { eq }) => eq(communes.id, input),
				with: {
					wilaya: true,
				},
			});

			return commune;
		}),
});
