import {
	chats,
	chatParticipants,
	cats,
	users,
	messages,
} from "@/lib/db/schema";
import { eq, and, not, inArray, count } from "drizzle-orm";
import { logSlowQuery } from "./cat-helpers";

// Reusable database query helpers for chat operations
export const chatHelpers = {
	// Check if user is participant in a chat
	async isUserParticipant(db: any, chatId: number, userId: number) {
		const startTime = performance.now();
		const participant = await db.query.chatParticipants.findFirst({
			where: and(
				eq(chatParticipants.chatId, chatId),
				eq(chatParticipants.userId, userId)
			),
		});
		const duration = performance.now() - startTime;
		logSlowQuery("isUserParticipant", duration);
		return !!participant;
	},

	// Get user's chat IDs efficiently
	async getUserChatIds(
		db: any,
		userId: number,
		limit?: number,
		offset?: number
	) {
		return await db
			.select({ chatId: chatParticipants.chatId })
			.from(chatParticipants)
			.where(eq(chatParticipants.userId, userId))
			.limit(limit || 50)
			.offset(offset || 0);
	},

	// Check if conversation exists between user and cat owner for a specific cat
	async findExistingChatForCat(db: any, catId: number, userId: number) {
		const startTime = performance.now();

		// First get the cat to know who the owner is
		const cat = await db.query.cats.findFirst({
			where: eq(cats.id, catId),
			columns: {
				userId: true,
			},
		});

		if (!cat) {
			const duration = performance.now() - startTime;
			logSlowQuery("findExistingChatForCat", duration);
			return null;
		}

		// Find a chat for this specific cat where the user is a participant
		const chat = await db.query.chats.findFirst({
			where: eq(chats.catId, catId),
			with: {
				participants: true,
			},
		});

		// Check if both the user and the cat owner are participants
		if (chat && chat.participants.length === 2) {
			const participantIds = chat.participants.map((p: any) => p.userId);
			const hasUser = participantIds.includes(userId);
			const hasCatOwner = participantIds.includes(cat.userId);

			if (hasUser && hasCatOwner) {
				const duration = performance.now() - startTime;
				logSlowQuery("findExistingChatForCat", duration);
				return chat;
			}
		}

		const duration = performance.now() - startTime;
		logSlowQuery("findExistingChatForCat", duration);
		return null;
	},

	// Get basic user info for chat participants
	async getChatParticipants(
		db: any,
		chatIds: number[],
		excludeUserId: number
	) {
		return await db.query.chatParticipants.findMany({
			where: and(
				inArray(chatParticipants.chatId, chatIds),
				not(eq(chatParticipants.userId, excludeUserId))
			),
			with: {
				user: {
					columns: {
						id: true,
						slug: true,
						name: true,
						image: true,
						role: true,
					},
				},
			},
		});
	},

	// Get cat owner info for a specific cat
	async getCatOwnerInfo(db: any, catId: number) {
		const startTime = performance.now();
		const cat = await db.query.cats.findFirst({
			where: eq(cats.id, catId),
			columns: {
				userId: true,
			},
			with: {
				user: {
					columns: {
						id: true,
						slug: true,
						name: true,
						image: true,
					},
				},
			},
		});
		const duration = performance.now() - startTime;
		logSlowQuery("getCatOwnerInfo", duration);

		return cat?.user || null;
	},

	// Check if user exists by ID
	async getUserById(db: any, userId: number) {
		return await db.query.users.findFirst({
			where: eq(users.id, userId),
			columns: {
				id: true,
				name: true,
				email: true,
				slug: true,
				role: true,
				image: true,
			},
		});
	},

	// Get chat with basic info (without heavy joins)
	async getChatBasicInfo(db: any, chatId: number) {
		return await db.query.chats.findFirst({
			where: eq(chats.id, chatId),
			columns: {
				id: true,
				catId: true,
				createdAt: true,
			},
		});
	},

	// Check if chat exists and user has access
	async validateChatAccess(db: any, chatId: number, userId: number) {
		const chat = await this.getChatBasicInfo(db, chatId);
		if (!chat) {
			return { valid: false, error: "Chat not found" };
		}

		const isParticipant = await this.isUserParticipant(db, chatId, userId);
		if (!isParticipant) {
			return {
				valid: false,
				error: "You are not a participant in this chat",
			};
		}

		return { valid: true, chat };
	},

	// Get all participants of a chat
	async getAllChatParticipants(db: any, chatId: number) {
		return await db.query.chatParticipants.findMany({
			where: eq(chatParticipants.chatId, chatId),
			with: {
				user: {
					columns: {
						id: true,
						slug: true,
						name: true,
						image: true,
						role: true,
					},
				},
			},
		});
	},

	// Count total messages in a chat
	async getChatMessageCount(db: any, chatId: number) {
		const result = await db
			.select({ count: count() })
			.from(messages)
			.where(eq(messages.chatId, chatId));

		return result[0]?.count || 0;
	},

	// Check if user has any chats
	async userHasChats(db: any, userId: number) {
		const result = await db
			.select({ count: count() })
			.from(chatParticipants)
			.where(eq(chatParticipants.userId, userId));

		return (result[0]?.count || 0) > 0;
	},

	// Optimized batch message status update
	async batchUpdateMessageStatus(
		db: any,
		messageIds: number[],
		status: "sent" | "delivered" | "read"
	) {
		if (messageIds.length === 0) return;

		const startTime = performance.now();
		await db
			.update(messages)
			.set({ status })
			.where(inArray(messages.id, messageIds));

		const duration = performance.now() - startTime;
		logSlowQuery("batchUpdateMessageStatus", duration, 200, {
			recordCount: messageIds.length,
		});
	},

	// Get chat with minimal data for access validation
	async getChatForValidation(db: any, chatId: number) {
		const startTime = performance.now();
		const chat = await db.query.chats.findFirst({
			where: eq(chats.id, chatId),
			columns: {
				id: true,
				catId: true,
			},
		});

		const duration = performance.now() - startTime;
		logSlowQuery("getChatForValidation", duration);
		return chat;
	},

	// Optimized message count for pagination
	async getMessageCount(db: any, chatId: number) {
		const startTime = performance.now();
		const result = await db
			.select({ count: count() })
			.from(messages)
			.where(eq(messages.chatId, chatId));

		const duration = performance.now() - startTime;
		logSlowQuery("getMessageCount", duration);
		return result[0]?.count || 0;
	},

	// Get unread message count for a specific chat and user
	async getUnreadMessageCount(db: any, chatId: number, userId: number) {
		const startTime = performance.now();
		const result = await db
			.select({ count: count() })
			.from(messages)
			.where(
				and(
					eq(messages.chatId, chatId),
					eq(messages.status, "sent"),
					not(eq(messages.userId, userId)) // Messages not from the current user
				)
			);

		const duration = performance.now() - startTime;
		logSlowQuery("getUnreadMessageCount", duration);
		return result[0]?.count || 0;
	},

	// Batch get unread message counts for multiple chats
	async getBatchUnreadMessageCounts(
		db: any,
		chatIds: number[],
		userId: number
	) {
		if (chatIds.length === 0) return new Map();

		const startTime = performance.now();
		const unreadCounts = await db
			.select({
				chatId: messages.chatId,
				count: count(),
			})
			.from(messages)
			.where(
				and(
					inArray(messages.chatId, chatIds),
					eq(messages.status, "sent"),
					not(eq(messages.userId, userId))
				)
			)
			.groupBy(messages.chatId);

		const duration = performance.now() - startTime;
		logSlowQuery("getBatchUnreadMessageCounts", duration, 100, {
			recordCount: chatIds.length,
		});

		// Convert to Map for easy lookup
		const countsMap = new Map();
		unreadCounts.forEach((item: any) => {
			countsMap.set(item.chatId, item.count);
		});

		return countsMap;
	},

	// Check if user is verified (clinic or rescuer role)
	async isUserVerified(db: any, userId: number) {
		const startTime = performance.now();
		const user = await db.query.users.findFirst({
			where: eq(users.id, userId),
			columns: {
				role: true,
			},
		});

		const duration = performance.now() - startTime;
		logSlowQuery("isUserVerified", duration);
		return user?.role === "clinic" || user?.role === "rescuer";
	},

	// Batch check verification status for multiple users
	async getBatchUserVerificationStatus(db: any, userIds: number[]) {
		if (userIds.length === 0) return new Map();

		const startTime = performance.now();
		const userRoles = await db.query.users.findMany({
			where: inArray(users.id, userIds),
			columns: {
				id: true,
				role: true,
			},
		});

		const duration = performance.now() - startTime;
		logSlowQuery("getBatchUserVerificationStatus", duration, 50, {
			recordCount: userIds.length,
		});

		// Convert to Map for easy lookup
		const verificationMap = new Map();
		userRoles.forEach((user: any) => {
			verificationMap.set(
				user.id,
				user.role === "clinic" || user.role === "rescuer"
			);
		});

		return verificationMap;
	},
};
