import { z } from "zod";
import { TRPCError } from "@trpc/server";
import { createTRPCRouter as router, protectedProcedure } from "../trpc";
import { favorites, cats } from "@/lib/db/schema";
import { eq, and } from "drizzle-orm";
import type { InferSelectModel } from "drizzle-orm";
import { logSlowQuery } from "./helpers/cat-helpers";

type FavoriteWithCat = InferSelectModel<typeof favorites> & {
	cat:
		| (InferSelectModel<typeof cats> & {
				images: { url: string; isPrimary: boolean }[];
				breed: { name: string } | null;
				wilaya: { name: string } | null;
		  })
		| null;
};

export const favoritesRouter = router({
	toggle: protectedProcedure
		.input(z.object({ catId: z.number() }))
		.mutation(async ({ ctx, input }) => {
			if (!ctx.user?.id) {
				throw new TRPCError({
					code: "UNAUTHORIZED",
					message: "You must be logged in to manage favorites",
				});
			}

			// Check if cat exists
			const cat = await ctx.db.query.cats.findFirst({
				where: eq(cats.id, input.catId),
			});

			if (!cat) {
				throw new TRPCError({
					code: "NOT_FOUND",
					message: "Cat not found",
				});
			}

			const existingFavorite = await ctx.db.query.favorites.findFirst({
				where: and(
					eq(favorites.userId, parseInt(ctx.user.id)),
					eq(favorites.catId, input.catId)
				),
			});

			if (existingFavorite) {
				await ctx.db
					.delete(favorites)
					.where(eq(favorites.id, existingFavorite.id));
				return { isFavorite: false };
			}

			await ctx.db.insert(favorites).values({
				userId: parseInt(ctx.user.id),
				catId: input.catId,
			});

			return { isFavorite: true };
		}),

	getUserFavorites: protectedProcedure.query(async ({ ctx }) => {
		const startTime = performance.now();

		if (!ctx.user?.id) {
			throw new TRPCError({
				code: "UNAUTHORIZED",
				message: "You must be logged in to view favorites",
			});
		}

		const userFavorites = (await ctx.db.query.favorites.findMany({
			where: eq(favorites.userId, parseInt(ctx.user.id)),
			with: {
				cat: {
					with: {
						images: true,
						breed: true,
						wilaya: true,
					},
				},
			},
		})) as FavoriteWithCat[];

		const duration = performance.now() - startTime;
		logSlowQuery("getUserFavorites", duration);

		// Filter out favorites where the cat no longer exists and map to the required format
		const validFavorites = userFavorites
			.filter((favorite) => favorite.cat !== null)
			.map((favorite) => {
				const cat = favorite.cat!;
				return {
					...cat,
					id: cat.id.toString(),
					name: cat.name,
					age: cat.age,
					gender: cat.gender,
					breed: cat.breed?.name || "Unknown",
					location: cat.wilaya?.name || "Unknown",
					imageUrl:
						cat.images?.find((img) => img.isPrimary)?.url ||
						cat.images?.[0]?.url ||
						"/cat.jpeg?height=300&width=400",
					vaccinated: cat.vaccinated,
					neutered: cat.neutered,
					specialNeeds: cat.specialNeeds,
					adopted: cat.adopted,
					isDraft: cat.isDraft,
					status: cat.status,
					createdAt: cat.createdAt,
					isFavorite: true,
				};
			});

		return validFavorites;
	}),

	isFavorite: protectedProcedure
		.input(z.object({ catId: z.number() }))
		.query(async ({ ctx, input }) => {
			if (!ctx.user?.id) {
				throw new TRPCError({
					code: "UNAUTHORIZED",
					message: "You must be logged in to check favorites",
				});
			}

			// Check if cat exists
			const cat = await ctx.db.query.cats.findFirst({
				where: eq(cats.id, input.catId),
			});

			if (!cat) {
				throw new TRPCError({
					code: "NOT_FOUND",
					message: "Cat not found",
				});
			}

			const favorite = await ctx.db.query.favorites.findFirst({
				where: and(
					eq(favorites.userId, parseInt(ctx.user.id)),
					eq(favorites.catId, input.catId)
				),
			});

			return !!favorite;
		}),
});
