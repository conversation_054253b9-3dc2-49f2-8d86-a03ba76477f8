import { z } from "zod";
import { createTRPCRouter as router, publicProcedure } from "../trpc";
import { logSlowQuery } from "./helpers/cat-helpers";

export const breedsRouter = router({
	getAll: publicProcedure.query(async ({ ctx }) => {
		const startTime = performance.now();
		const breeds = await ctx.db.query.catBreeds.findMany({
			orderBy: (catBreeds, { asc }) => [asc(catBreeds.name)],
		});
		const duration = performance.now() - startTime;
		logSlowQuery("getAllBreeds", duration);

		return breeds;
	}),

	getById: publicProcedure.input(z.number()).query(async ({ ctx, input }) => {
		const startTime = performance.now();
		const breed = await ctx.db.query.catBreeds.findFirst({
			where: (catBreeds, { eq }) => eq(catBreeds.id, input),
		});
		const duration = performance.now() - startTime;
		logSlowQuery("getBreedById", duration);

		return breed;
	}),
});
